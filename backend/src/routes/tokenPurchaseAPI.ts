import express from 'express';
import { body, param, validationResult } from 'express-validator';
import { authenticate } from '@/middleware/auth';
import { tokenPurchaseService, TOKEN_PACKAGES } from '@/services/tokenPurchaseService';
import { logger } from '@/utils/logger';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
    return;
  }
  next();
};

/**
 * GET /api/token-purchase/packages
 * Get all available token packages
 */
router.get('/packages', (req: express.Request, res: express.Response) => {
  try {
    const packages = tokenPurchaseService.getTokenPackages();
    
    res.json({
      success: true,
      message: 'Token packages retrieved successfully',
      data: packages
    });
  } catch (error: any) {
    logger.error('Failed to get token packages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve token packages'
    });
  }
});

/**
 * POST /api/token-purchase/initiate
 * Initiate a token purchase
 */
router.post('/initiate',
  authenticate,
  [
    body('packageId')
      .isIn(Object.keys(TOKEN_PACKAGES))
      .withMessage('Invalid package ID'),
    body('phoneNumber')
      .matches(/^(254[17]\d{8}|0[17]\d{8})$/)
      .withMessage('Valid Kenyan phone number is required'),
    body('paymentMethod')
      .optional()
      .isIn(['mpesa'])
      .withMessage('Invalid payment method')
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { packageId, phoneNumber, paymentMethod = 'mpesa' } = req.body;
      const userId = (req as any).user.id;

      const result = await tokenPurchaseService.initiateTokenPurchase({
        userId,
        packageId,
        phoneNumber,
        paymentMethod
      });

      if (result.success) {
        res.status(201).json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error: any) {
      logger.error('Token purchase initiation failed:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

/**
 * GET /api/token-purchase/status/:paymentId
 * Get payment status
 */
router.get('/status/:paymentId',
  authenticate,
  [
    param('paymentId')
      .notEmpty()
      .withMessage('Payment ID is required')
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { paymentId } = req.params;
      
      const result = await tokenPurchaseService.getPaymentStatus(paymentId);
      
      if (result.success) {
        res.json(result);
      } else {
        res.status(404).json(result);
      }

    } catch (error: any) {
      logger.error('Failed to get payment status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve payment status'
      });
    }
  }
);

/**
 * POST /api/token-purchase/callback
 * M-Pesa payment callback endpoint
 */
router.post('/callback', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    logger.info('M-Pesa callback received:', req.body);

    await tokenPurchaseService.processPaymentCallback(req.body);

    res.json({
      success: true,
      message: 'Callback processed successfully'
    });

  } catch (error: any) {
    logger.error('Failed to process M-Pesa callback:', error);
    
    // Always return success to M-Pesa to avoid retries
    res.json({
      success: true,
      message: 'Callback received'
    });
  }
});

/**
 * GET /api/token-purchase/history
 * Get user's token purchase history
 */
router.get('/history',
  authenticate,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Import Payment model here to avoid circular dependencies
      const Payment = require('@/models/Payment').default;
      
      const payments = await Payment.find({
        user: userId,
        'relatedEntity.type': 'token_purchase'
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

      const total = await Payment.countDocuments({
        user: userId,
        'relatedEntity.type': 'token_purchase'
      });

      res.json({
        success: true,
        message: 'Purchase history retrieved successfully',
        data: {
          payments,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error: any) {
      logger.error('Failed to get purchase history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve purchase history'
      });
    }
  }
);

/**
 * POST /api/token-purchase/retry/:paymentId
 * Retry a failed payment
 */
router.post('/retry/:paymentId',
  authenticate,
  [
    param('paymentId')
      .notEmpty()
      .withMessage('Payment ID is required')
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { paymentId } = req.params;
      const userId = (req as any).user.id;

      // Import Payment model
      const Payment = require('@/models/Payment').default;
      
      const payment = await Payment.findOne({
        paymentId,
        user: userId,
        status: 'failed'
      });

      if (!payment) {
        res.status(404).json({
          success: false,
          message: 'Payment not found or cannot be retried'
        });
        return;
      }

      if (payment.retryCount >= payment.maxRetries) {
        res.status(400).json({
          success: false,
          message: 'Maximum retry attempts exceeded'
        });
        return;
      }

      // Extract package info from metadata
      const { packageId } = payment.metadata;
      const phoneNumber = payment.mpesaDetails?.phoneNumber;

      if (!packageId || !phoneNumber) {
        res.status(400).json({
          success: false,
          message: 'Invalid payment data for retry'
        });
        return;
      }

      // Initiate new payment
      const result = await tokenPurchaseService.initiateTokenPurchase({
        userId,
        packageId,
        phoneNumber,
        paymentMethod: 'mpesa'
      });

      if (result.success) {
        // Mark old payment as cancelled
        payment.status = 'cancelled';
        payment.statusHistory.push({
          status: 'cancelled',
          timestamp: new Date(),
          source: 'system',
          note: 'Payment cancelled due to retry'
        });
        await payment.save();

        res.json(result);
      } else {
        res.status(400).json(result);
      }

    } catch (error: any) {
      logger.error('Payment retry failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retry payment'
      });
    }
  }
);

/**
 * GET /api/token-purchase/stats
 * Get user's token purchase statistics
 */
router.get('/stats',
  authenticate,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const userId = (req as any).user.id;

      // Import models
      const Payment = require('@/models/Payment').default;
      const User = require('@/models/User').default;

      const user = await User.findById(userId).select('pediTokens totalTokensPurchased');
      
      const stats = await Payment.aggregate([
        {
          $match: {
            user: new (require('mongoose')).Types.ObjectId(userId),
            'relatedEntity.type': 'token_purchase'
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' }
          }
        }
      ]);

      const formattedStats = {
        currentBalance: user?.pediTokens || 0,
        totalPurchased: user?.totalTokensPurchased || 0,
        paymentStats: stats.reduce((acc: any, stat: any) => {
          acc[stat._id] = {
            count: stat.count,
            totalAmount: stat.totalAmount
          };
          return acc;
        }, {})
      };

      res.json({
        success: true,
        message: 'Statistics retrieved successfully',
        data: formattedStats
      });

    } catch (error: any) {
      logger.error('Failed to get purchase stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve statistics'
      });
    }
  }
);

export default router;
