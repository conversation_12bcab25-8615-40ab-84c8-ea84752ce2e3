import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate, AuthenticatedRequest } from '@/middleware/auth';
import { notificationService } from '@/services/notification';
import NotificationPreferences from '@/models/NotificationPreferences';
import NotificationTemplate from '@/models/NotificationTemplate';
import { INotificationPreferencesModel, INotificationTemplateModel } from '@/types/models';
import { logger } from '@/utils/logger';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array(),
    });
    return;
  }
  next();
};

/**
 * GET /api/notifications
 * Get user's notifications
 */
router.get('/',
  authenticate,
  [
    query('type').optional().isIn(['sms', 'push', 'email', 'in_app']),
    query('category').optional().isIn(['payment', 'exchange', 'system', 'promotion', 'reminder', 'welcome', 'token', 'security']),
    query('status').optional().isIn(['pending', 'sent', 'delivered', 'failed', 'cancelled']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('skip').optional().isInt({ min: 0 }).toInt(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user?._id?.toString();
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }

      const notifications = await notificationService.getUserNotifications(userId, {
        type: req.query.type as string,
        category: req.query.category as string,
        status: req.query.status as string,
        limit: req.query.limit ? Number(req.query.limit) : undefined,
        skip: req.query.skip ? Number(req.query.skip) : undefined,
      });

      res.json({
        success: true,
        data: notifications,
        count: notifications.length,
      });

    } catch (error: any) {
      logger.error('Failed to get notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notifications',
        error: error.message,
      });
    }
  }
);

/**
 * POST /api/notifications
 * Create a new notification
 */
router.post('/',
  authenticate,
  [
    body('userId').optional().isMongoId(),
    body('type').optional().isIn(['sms', 'push', 'email', 'in_app']),
    body('category').isIn(['payment', 'exchange', 'system', 'promotion', 'reminder', 'welcome', 'token', 'security']),
    body('title').optional().isLength({ min: 1, max: 100 }),
    body('message').optional().isLength({ min: 1, max: 1000 }),
    body('templateId').optional().isMongoId(),
    body('templateVariables').optional().isObject(),
    body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    body('scheduledAt').optional().isISO8601().toDate(),
    body('phoneNumber').optional().isMobilePhone('any'),
    body('maxAttempts').optional().isInt({ min: 1, max: 10 }),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const currentUserId = req.user?._id?.toString();
      if (!currentUserId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }

      // Use current user ID if not specified (for self-notifications)
      const targetUserId = req.body.userId || currentUserId;

      // Only allow admins to send notifications to other users
      if (targetUserId !== currentUserId && req.user?.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions to send notifications to other users',
        });
        return;
      }

      const notification = await notificationService.createNotification({
        userId: targetUserId,
        type: req.body.type,
        category: req.body.category,
        title: req.body.title,
        message: req.body.message,
        templateId: req.body.templateId,
        templateVariables: req.body.templateVariables,
        priority: req.body.priority,
        scheduledAt: req.body.scheduledAt,
        phoneNumber: req.body.phoneNumber,
        maxAttempts: req.body.maxAttempts,
      });

      res.status(201).json({
        success: true,
        data: notification,
        message: 'Notification created successfully',
      });

    } catch (error: any) {
      logger.error('Failed to create notification:', error);
      res.status(400).json({
        success: false,
        message: 'Failed to create notification',
        error: error.message,
      });
    }
  }
);

/**
 * POST /api/notifications/bulk
 * Create bulk notifications
 */
router.post('/bulk',
  authenticate,
  [
    body('userIds').isArray({ min: 1, max: 1000 }),
    body('userIds.*').isMongoId(),
    body('type').optional().isIn(['sms', 'push', 'email', 'in_app']),
    body('category').isIn(['payment', 'exchange', 'system', 'promotion', 'reminder', 'welcome', 'token', 'security']),
    body('templateId').isMongoId(),
    body('templateVariables').optional().isObject(),
    body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']),
    body('scheduledAt').optional().isISO8601().toDate(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      // Only admins can send bulk notifications
      if (req.user?.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions for bulk notifications',
        });
        return;
      }

      const result = await notificationService.createBulkNotifications({
        userIds: req.body.userIds,
        type: req.body.type,
        category: req.body.category,
        templateId: req.body.templateId,
        templateVariables: req.body.templateVariables,
        priority: req.body.priority,
        scheduledAt: req.body.scheduledAt,
      });

      res.status(201).json({
        success: true,
        data: result,
        message: `Bulk notifications created: ${result.created} successful, ${result.failed} failed`,
      });

    } catch (error: any) {
      logger.error('Failed to create bulk notifications:', error);
      res.status(400).json({
        success: false,
        message: 'Failed to create bulk notifications',
        error: error.message,
      });
    }
  }
);

/**
 * PATCH /api/notifications/:id/read
 * Mark notification as read
 */
router.patch('/:id/read',
  authenticate,
  [
    param('id').isMongoId(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user?._id?.toString();
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }

      const success = await notificationService.markAsRead(req.params.id, userId);

      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Notification not found or cannot be marked as read',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification marked as read',
      });

    } catch (error: any) {
      logger.error('Failed to mark notification as read:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to mark notification as read',
        error: error.message,
      });
    }
  }
);

/**
 * DELETE /api/notifications/:id
 * Cancel a pending notification
 */
router.delete('/:id',
  authenticate,
  [
    param('id').isMongoId(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user?._id?.toString();
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }

      // Allow admins to cancel any notification, users can only cancel their own
      const targetUserId = req.user?.role === 'admin' ? undefined : userId;
      
      const success = await notificationService.cancelNotification(req.params.id, targetUserId);

      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Notification not found or cannot be cancelled',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification cancelled successfully',
      });

    } catch (error: any) {
      logger.error('Failed to cancel notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel notification',
        error: error.message,
      });
    }
  }
);

/**
 * POST /api/notifications/:id/retry
 * Retry a failed notification
 */
router.post('/:id/retry',
  authenticate,
  [
    param('id').isMongoId(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      // Only admins can retry notifications
      if (req.user?.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions to retry notifications',
        });
        return;
      }

      const success = await notificationService.retryNotification(req.params.id);

      if (!success) {
        res.status(400).json({
          success: false,
          message: 'Notification cannot be retried',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification retry initiated',
      });

    } catch (error: any) {
      logger.error('Failed to retry notification:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retry notification',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/notifications/preferences
 * Get user's notification preferences
 */
router.get('/preferences',
  authenticate,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user?._id?.toString();
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }

      const preferences = await (NotificationPreferences as INotificationPreferencesModel).getOrCreateForUser(userId);

      res.json({
        success: true,
        data: preferences,
      });

    } catch (error: any) {
      logger.error('Failed to get notification preferences:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification preferences',
        error: error.message,
      });
    }
  }
);

/**
 * PUT /api/notifications/preferences
 * Update user's notification preferences
 */
router.put('/preferences',
  authenticate,
  [
    body('isEnabled').optional().isBoolean(),
    body('channels').optional().isObject(),
    body('categories').optional().isObject(),
    body('quietHours').optional().isObject(),
    body('frequency').optional().isObject(),
    body('language').optional().isIn(['en', 'sw']),
    body('locale').optional().isString(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user?._id?.toString();
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated',
        });
        return;
      }

      const preferences = await (NotificationPreferences as any).getOrCreateForUser(userId);

      // Update preferences
      Object.assign(preferences, req.body);
      preferences.lastModifiedBy = userId;

      await preferences.save();

      res.json({
        success: true,
        data: preferences,
        message: 'Notification preferences updated successfully',
      });

    } catch (error: any) {
      logger.error('Failed to update notification preferences:', error);
      res.status(400).json({
        success: false,
        message: 'Failed to update notification preferences',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/notifications/templates
 * Get notification templates
 */
router.get('/templates',
  authenticate,
  [
    query('category').optional().isIn(['payment', 'exchange', 'system', 'promotion', 'reminder', 'welcome', 'token', 'security']),
    query('type').optional().isIn(['sms', 'push', 'email', 'in_app']),
    query('active').optional().isBoolean(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      let templates: any[];

      if (req.query.category) {
        templates = await (NotificationTemplate as INotificationTemplateModel).findByCategory(
          req.query.category as string,
          req.query.type as string
        );
      } else {
        const query: any = {};
        if (req.query.type) query.type = req.query.type;
        if (req.query.active !== undefined) query.isActive = req.query.active === 'true';

        templates = await NotificationTemplate.find(query).sort({ category: 1, name: 1 });
      }

      res.json({
        success: true,
        data: templates,
        count: templates.length,
      });

    } catch (error: any) {
      logger.error('Failed to get notification templates:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification templates',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/notifications/stats
 * Get notification statistics
 */
router.get('/stats',
  authenticate,
  [
    query('startDate').optional().isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user?.role === 'admin' ? undefined : req.user?._id?.toString();

      const dateRange = req.query.startDate && req.query.endDate ? {
        startDate: new Date(req.query.startDate as string),
        endDate: new Date(req.query.endDate as string),
      } : undefined;

      const stats = await notificationService.getNotificationStats(userId, dateRange);

      res.json({
        success: true,
        data: stats,
      });

    } catch (error: any) {
      logger.error('Failed to get notification stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get notification stats',
        error: error.message,
      });
    }
  }
);

/**
 * GET /api/notifications/health
 * Get notification service health
 */
router.get('/health',
  authenticate,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      // Only admins can check service health
      if (req.user?.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
        });
        return;
      }

      const health = await notificationService.getServiceHealth();

      res.json({
        success: true,
        data: health,
      });

    } catch (error: any) {
      logger.error('Failed to get service health:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get service health',
        error: error.message,
      });
    }
  }
);

/**
 * POST /api/notifications/webhook/delivery
 * Webhook for delivery status updates from Africa's Talking
 */
router.post('/webhook/delivery',
  [
    body('id').isString(),
    body('status').isString(),
    body('phoneNumber').optional().isString(),
    body('cost').optional().isString(),
    body('deliveredAt').optional().isISO8601().toDate(),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { id: messageId, status, deliveredAt } = req.body;

      const success = await notificationService.updateDeliveryStatus(
        messageId,
        status,
        deliveredAt
      );

      if (success) {
        res.json({
          success: true,
          message: 'Delivery status updated',
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'Notification not found',
        });
      }

    } catch (error: any) {
      logger.error('Failed to process delivery webhook:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process delivery webhook',
        error: error.message,
      });
    }
  }
);

export default router;
