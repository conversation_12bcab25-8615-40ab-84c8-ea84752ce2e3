import { Router } from 'express';
import { validationResult } from 'express-validator';
import multer from 'multer';
import User from '@/models/User';
import { authenticate, authorize } from '@/middleware/auth';
import { AuthenticatedRequest } from '@/types/auth';
import { createError } from '@/middleware/errorHandler';
import { cloudinaryService } from '@/services/cloudinary';
import { tokenService } from '@/services/token';
import { calculateSustainabilityScore } from '@/utils/helpers';
import { validatePagination } from '@/utils/validation';
import { logger } from '@/utils/logger';

const router = Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Get user profile
router.get('/profile', authenticate, async (req: AuthenticatedRequest, res, next) => {
  try {
    const user = req.user;

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          phoneNumber: user.phoneNumber,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          profilePicture: user.profilePicture,
          location: user.location,
          isVerified: user.isVerified,
          role: user.role,
          pediTokens: user.pediTokens,
          rating: user.rating,
          sustainabilityScore: user.sustainabilityScore,
          totalDonations: user.totalDonations,
          totalSwaps: user.totalSwaps,
          preferences: user.preferences,
          joinedAt: user.createdAt,
          lastActive: user.lastActive
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Update user profile
router.put('/profile', authenticate, async (req: AuthenticatedRequest, res, next) => {
  try {
    const user = req.user;
    const {
      firstName,
      lastName,
      email,
      location,
      preferences
    } = req.body;

    // Validate email if provided
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email, _id: { $ne: user._id } });
      if (existingUser) {
        return next(createError('Email already in use', 409));
      }
    }

    // Update user fields
    if (firstName) user.firstName = firstName.trim();
    if (lastName) user.lastName = lastName.trim();
    if (email !== undefined) user.email = email;
    if (location) user.location = { ...user.location, ...location };
    if (preferences) user.preferences = { ...user.preferences, ...preferences };

    // Recalculate sustainability score
    user.sustainabilityScore = calculateSustainabilityScore({
      donationCount: user.totalDonations,
      swapCount: user.totalSwaps,
      listingCount: 0, // TODO: Get from clothing items
      accountAge: Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)),
      qualityRating: user.rating.average
    });

    await user.save();

    logger.info('User profile updated', {
      userId: user._id,
      updatedFields: Object.keys(req.body)
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: user._id,
          phoneNumber: user.phoneNumber,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          profilePicture: user.profilePicture,
          location: user.location,
          isVerified: user.isVerified,
          role: user.role,
          pediTokens: user.pediTokens,
          rating: user.rating,
          sustainabilityScore: user.sustainabilityScore,
          totalDonations: user.totalDonations,
          totalSwaps: user.totalSwaps,
          preferences: user.preferences,
          joinedAt: user.createdAt,
          lastActive: user.lastActive
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Upload profile picture
router.post('/profile/picture', authenticate, upload.single('profilePicture'), async (req: AuthenticatedRequest, res, next) => {
  try {
    const user = req.user;

    if (!req.file) {
      return next(createError('No image file provided', 400));
    }

    // Upload to Cloudinary
    const uploadResult = await cloudinaryService.uploadImage(req.file.buffer, {
      folder: 'pedi/profiles',
      publicId: `profile_${user._id}`,
      transformation: [
        { width: 400, height: 400, crop: 'fill', gravity: 'face' },
        { quality: 'auto', fetch_format: 'auto' }
      ]
    });

    if (!uploadResult.success) {
      return next(createError('Failed to upload image', 500));
    }

    // Delete old profile picture if exists
    if (user.profilePicture) {
      // Extract public ID from URL and delete
      const publicId = user.profilePicture.split('/').pop()?.split('.')[0];
      if (publicId) {
        await cloudinaryService.deleteImage(`pedi/profiles/${publicId}`);
      }
    }

    // Update user profile picture
    user.profilePicture = uploadResult.url;
    await user.save();

    logger.info('Profile picture updated', {
      userId: user._id,
      imageUrl: uploadResult.url
    });

    res.json({
      success: true,
      message: 'Profile picture updated successfully',
      data: {
        profilePicture: uploadResult.url
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get user token balance and history
router.get('/tokens', authenticate, validatePagination, async (req: AuthenticatedRequest, res, next) => {
  try {
    const user = req.user;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const type = req.query.type as string;

    // Get token history
    const tokenHistory = await tokenService.getTokenHistory(user._id.toString(), {
      page,
      limit,
      type: type as any
    });

    if (!tokenHistory.success) {
      return next(createError(tokenHistory.error || 'Failed to get token history', 500));
    }

    res.json({
      success: true,
      data: {
        balance: user.pediTokens,
        transactions: tokenHistory.transactions,
        pagination: tokenHistory.pagination
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get user listings
router.get('/listings', authenticate, validatePagination, async (req, res, next) => {
  try {
    // TODO: Implement when clothing items are ready
    res.json({
      success: true,
      message: 'User listings endpoint - Coming soon',
      data: {
        listings: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get user transactions
router.get('/transactions', authenticate, validatePagination, async (req, res, next) => {
  try {
    // TODO: Implement when transactions are ready
    res.json({
      success: true,
      message: 'User transactions endpoint - Coming soon',
      data: {
        transactions: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Deactivate account
router.post('/deactivate', authenticate, async (req: AuthenticatedRequest, res, next) => {
  try {
    const user = req.user;
    const { reason } = req.body;

    user.isActive = false;
    user.deactivatedAt = new Date();
    user.deactivationReason = reason;

    await user.save();

    logger.info('User account deactivated', {
      userId: user._id,
      reason
    });

    res.json({
      success: true,
      message: 'Account deactivated successfully'
    });

  } catch (error) {
    next(error);
  }
});

export default router;
