import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import CharityPartner from '../models/CharityPartner';
import { authenticate } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth';
import { trackUserActivity } from '../middleware/analytics';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
    return;
  }
  next();
};

// Get all charity partners (public endpoint with filtering)
router.get('/',
  [
    query('category').optional().isString().withMessage('Category must be a string'),
    query('county').optional().isString().withMessage('County must be a string'),
    query('town').optional().isString().withMessage('Town must be a string'),
    query('acceptedItemTypes').optional().isString().withMessage('Accepted item types must be a string'),
    query('verificationStatus').optional().isIn(['pending', 'verified', 'suspended']).withMessage('Invalid verification status'),
    query('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    query('search').optional().isString().withMessage('Search must be a string'),
    query('sortBy').optional().isIn(['name', 'createdAt', 'averageRating', 'totalDonationsReceived']).withMessage('Invalid sort field'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const {
        category,
        county,
        town,
        acceptedItemTypes,
        verificationStatus = 'verified',
        isActive = true,
        page = 1,
        limit = 10,
        search,
        sortBy = 'name',
        sortOrder = 'asc'
      } = req.query;

      // Build filter object
      const filter: any = {
        verificationStatus,
        isActive: isActive === 'true'
      };

      if (category) filter.category = category;
      if (county) filter['contactInfo.address.county'] = county;
      if (town) filter['contactInfo.address.town'] = town;
      if (acceptedItemTypes) {
        filter.acceptedItemTypes = { $in: (acceptedItemTypes as string).split(',') };
      }

      // Add search functionality
      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
          { category: { $regex: search, $options: 'i' } }
        ];
      }

      // Calculate pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      // Build sort object
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      // Execute query with pagination
      const [charityPartners, totalCount] = await Promise.all([
        CharityPartner.find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limitNum)
          .select('-verifiedBy -__v'),
        CharityPartner.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(totalCount / limitNum);

      res.json({
        success: true,
        data: {
          charityPartners,
          pagination: {
            currentPage: pageNum,
            totalPages,
            totalItems: totalCount,
            itemsPerPage: limitNum,
            hasNextPage: pageNum < totalPages,
            hasPrevPage: pageNum > 1
          }
        }
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch charity partners'
      });
    }
  }
);

// Get charity partner by ID (public endpoint)
router.get('/:id',
  [
    param('id').isMongoId().withMessage('Invalid charity partner ID'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { id } = req.params;

      const charityPartner = await CharityPartner.findById(id)
        .select('-verifiedBy -__v');

      if (!charityPartner) {
        res.status(404).json({
          success: false,
          message: 'Charity partner not found'
        });
        return;
      }

      res.json({
        success: true,
        data: charityPartner
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch charity partner'
      });
    }
  }
);

// Register new charity partner
router.post('/register',
  [
    body('name').isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
    body('description').isLength({ min: 10, max: 500 }).withMessage('Description must be between 10 and 500 characters'),
    body('category').isIn([
      'Children & Youth', 'Women Empowerment', 'Education', 'Healthcare',
      'Environment', 'Community Development', 'Disaster Relief', 'Elderly Care',
      'Disability Support', 'General Welfare'
    ]).withMessage('Invalid category'),
    body('contactInfo.email').isEmail().withMessage('Valid email is required'),
    body('contactInfo.phone').matches(/^(\+254|0)[17]\d{8}$/).withMessage('Valid Kenyan phone number is required'),
    body('contactInfo.website').optional().isURL().withMessage('Valid website URL required'),
    body('contactInfo.address.county').isString().withMessage('County is required'),
    body('contactInfo.address.town').isString().withMessage('Town is required'),
    body('contactInfo.address.specificLocation').isString().withMessage('Specific location is required'),
    body('acceptedItemTypes').isArray({ min: 1 }).withMessage('At least one accepted item type is required'),
    body('requirements.condition').isArray({ min: 1 }).withMessage('At least one condition requirement is required'),
    body('requirements.categories').isArray({ min: 1 }).withMessage('At least one category requirement is required'),
    body('requirements.notes').optional().isLength({ max: 500 }).withMessage('Requirements notes too long'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const charityPartnerData = req.body;

      // Check if charity partner with same email already exists
      const existingPartner = await CharityPartner.findOne({
        'contactInfo.email': charityPartnerData.contactInfo.email
      });

      if (existingPartner) {
        res.status(400).json({
          success: false,
          message: 'Charity partner with this email already exists'
        });
        return;
      }

      // Create new charity partner
      const charityPartner = new CharityPartner({
        ...charityPartnerData,
        verificationStatus: 'pending',
        isActive: false // Will be activated after verification
      });

      await charityPartner.save();

      res.status(201).json({
        success: true,
        message: 'Charity partner registration submitted successfully. Verification pending.',
        data: {
          id: charityPartner._id,
          name: charityPartner.name,
          verificationStatus: charityPartner.verificationStatus
        }
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to register charity partner'
      });
    }
  }
);

// Update charity partner profile (authenticated charity partner only)
router.put('/:id/profile',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid charity partner ID'),
    body('name').optional().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
    body('description').optional().isLength({ min: 10, max: 500 }).withMessage('Description must be between 10 and 500 characters'),
    body('contactInfo.email').optional().isEmail().withMessage('Valid email is required'),
    body('contactInfo.phone').optional().matches(/^(\+254|0)[17]\d{8}$/).withMessage('Valid Kenyan phone number is required'),
    body('contactInfo.website').optional().isURL().withMessage('Valid website URL required'),
    body('contactInfo.address.county').optional().isString().withMessage('County is required'),
    body('contactInfo.address.town').optional().isString().withMessage('Town is required'),
    body('contactInfo.address.specificLocation').optional().isString().withMessage('Specific location is required'),
    body('acceptedItemTypes').optional().isArray({ min: 1 }).withMessage('At least one accepted item type is required'),
    body('requirements.condition').optional().isArray({ min: 1 }).withMessage('At least one condition requirement is required'),
    body('requirements.categories').optional().isArray({ min: 1 }).withMessage('At least one category requirement is required'),
    body('requirements.notes').optional().isLength({ max: 500 }).withMessage('Requirements notes too long'),
    body('settings.autoAcceptDonations').optional().isBoolean().withMessage('Auto accept donations must be boolean'),
    body('settings.requireQualityCheck').optional().isBoolean().withMessage('Require quality check must be boolean'),
    body('settings.providesPickup').optional().isBoolean().withMessage('Provides pickup must be boolean'),
    body('settings.maxPickupDistance').optional().isInt({ min: 0 }).withMessage('Max pickup distance must be non-negative'),
    body('settings.issuesReceipts').optional().isBoolean().withMessage('Issues receipts must be boolean'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Find charity partner
      const charityPartner = await CharityPartner.findById(id);
      if (!charityPartner) {
        res.status(404).json({
          success: false,
          message: 'Charity partner not found'
        });
        return;
      }

      // TODO: Add authorization check - only the charity partner themselves or admin can update
      // For now, allowing any authenticated user

      // Update charity partner
      Object.assign(charityPartner, updateData);
      await charityPartner.save();

      res.json({
        success: true,
        message: 'Charity partner profile updated successfully',
        data: charityPartner
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to update charity partner profile'
      });
    }
  }
);

// Admin: Verify charity partner
router.post('/:id/verify',
  authenticate,
  trackUserActivity('charity_partner_verified'),
  [
    param('id').isMongoId().withMessage('Invalid charity partner ID'),
    body('verificationStatus').isIn(['verified', 'suspended']).withMessage('Invalid verification status'),
    body('verificationNotes').optional().isLength({ max: 1000 }).withMessage('Verification notes too long'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { verificationStatus, verificationNotes } = req.body;
      const verifierId = (req as AuthenticatedRequest).user!._id;

      // TODO: Add admin role check
      // For now, allowing any authenticated user to verify

      const charityPartner = await CharityPartner.findById(id);
      if (!charityPartner) {
        res.status(404).json({
          success: false,
          message: 'Charity partner not found'
        });
        return;
      }

      // Update verification status
      charityPartner.verificationStatus = verificationStatus;
      charityPartner.verifiedBy = verifierId;
      charityPartner.verificationDate = new Date();
      charityPartner.isActive = verificationStatus === 'verified';

      await charityPartner.save();

      res.json({
        success: true,
        message: `Charity partner ${verificationStatus} successfully`,
        data: {
          id: charityPartner._id,
          name: charityPartner.name,
          verificationStatus: charityPartner.verificationStatus,
          verificationDate: charityPartner.verificationDate
        }
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to verify charity partner'
      });
    }
  }
);

// Get charity partner statistics
router.get('/:id/stats',
  [
    param('id').isMongoId().withMessage('Invalid charity partner ID'),
    query('period').optional().isIn(['7d', '30d', '90d', '1y', 'all']).withMessage('Invalid period'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { period = '30d' } = req.query;

      const charityPartner = await CharityPartner.findById(id);
      if (!charityPartner) {
        res.status(404).json({
          success: false,
          message: 'Charity partner not found'
        });
        return;
      }

      // TODO: Calculate date range based on period for filtering statistics
      // let startDate: Date | undefined;
      // if (period !== 'all') {
      //   const now = new Date();
      //   switch (period) {
      //     case '7d':
      //       startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      //       break;
      //     case '30d':
      //       startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      //       break;
      //     case '90d':
      //       startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      //       break;
      //     case '1y':
      //       startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      //       break;
      //   }
      // }

      // TODO: Implement actual statistics calculation from Transaction model
      // For now, returning the basic stats from the charity partner model
      const stats = {
        basic: charityPartner.stats,
        period: period,
        periodStats: {
          // These would be calculated from actual transaction data
          donationsReceived: 0,
          itemsReceived: 0,
          newBeneficiaries: 0,
          totalValue: 0,
          averageResponseTime: charityPartner.stats.responseTime
        },
        trends: {
          // These would show trends over time
          donationsTrend: 'stable',
          responseTrend: 'improving',
          ratingTrend: 'stable'
        }
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch charity partner statistics'
      });
    }
  }
);

export default router;
