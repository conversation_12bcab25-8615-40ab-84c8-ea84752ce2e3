import express from 'express';
import { body, param, validationResult } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth';
import { paymentIntegrationService } from '../services/paymentIntegration';
import { paymentService } from '../services/payment';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
    return;
  }
  next();
};

/**
 * Process platform fee payment for token purchase
 * POST /api/payment-integration/platform-fee
 */
router.post('/platform-fee',
  authenticate,
  [
    body('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('feeAmount').isNumeric().isFloat({ min: 1 }).withMessage('Fee amount must be positive'),
    body('paymentMethodId').optional().isMongoId().withMessage('Invalid payment method ID'),
    body('phoneNumber').optional().matches(/^(\+254|254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { transactionId, feeAmount, paymentMethodId, phoneNumber } = req.body;

      const result = await paymentIntegrationService.processTokenPurchaseFee({
        transactionId,
        feeAmount,
        paymentMethodId,
        phoneNumber,
      });

      res.status(201).json({
        success: true,
        message: 'Platform fee payment initiated',
        data: {
          paymentId: result.payment.paymentId,
          amount: result.payment.amount,
          transactionId: result.transaction._id,
        },
      });
    } catch (error: any) {
      console.error('Platform fee payment error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to process platform fee payment',
      });
    }
  }
);

/**
 * Process token top-up payment
 * POST /api/payment-integration/token-topup
 */
router.post('/token-topup',
  authenticate,
  [
    body('tokenAmount').isNumeric().isFloat({ min: 1 }).withMessage('Token amount must be positive'),
    body('cashAmount').isNumeric().isFloat({ min: 1 }).withMessage('Cash amount must be positive'),
    body('paymentMethodId').optional().isMongoId().withMessage('Invalid payment method ID'),
    body('phoneNumber').optional().matches(/^(\+254|254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response): Promise<void> => {
    try {
      const userId = req.user!.id;
      const { tokenAmount, cashAmount, paymentMethodId, phoneNumber } = req.body;

      const result = await paymentIntegrationService.processTokenTopUp({
        userId,
        tokenAmount,
        cashAmount,
        paymentMethodId,
        phoneNumber,
      });

      res.status(201).json({
        success: true,
        message: 'Token top-up payment initiated',
        data: {
          paymentId: result.payment.paymentId,
          amount: result.payment.amount,
          tokenAmount,
        },
      });
    } catch (error: any) {
      console.error('Token top-up payment error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to process token top-up payment',
      });
    }
  }
);

/**
 * Process delivery fee payment
 * POST /api/payment-integration/delivery-fee
 */
router.post('/delivery-fee',
  authenticate,
  [
    body('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('deliveryFee').isNumeric().isFloat({ min: 1 }).withMessage('Delivery fee must be positive'),
    body('paymentMethodId').optional().isMongoId().withMessage('Invalid payment method ID'),
    body('phoneNumber').optional().matches(/^(\+254|254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { transactionId, deliveryFee, paymentMethodId, phoneNumber } = req.body;

      const payment = await paymentIntegrationService.processDeliveryFee(
        transactionId,
        deliveryFee,
        paymentMethodId,
        phoneNumber
      );

      res.status(201).json({
        success: true,
        message: 'Delivery fee payment initiated',
        data: {
          paymentId: payment.paymentId,
          amount: payment.amount,
          transactionId,
        },
      });
    } catch (error: any) {
      console.error('Delivery fee payment error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to process delivery fee payment',
      });
    }
  }
);

/**
 * Get payment summary for a transaction
 * GET /api/payment-integration/transaction/:transactionId/summary
 */
router.get('/transaction/:transactionId/summary',
  authenticate,
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { transactionId } = req.params;

      const summary = await paymentIntegrationService.getTransactionPaymentSummary(transactionId);

      res.json({
        success: true,
        data: summary,
      });
    } catch (error: any) {
      console.error('Payment summary error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to get payment summary',
      });
    }
  }
);

/**
 * Calculate platform fee for a token amount
 * POST /api/payment-integration/calculate-platform-fee
 */
router.post('/calculate-platform-fee',
  authenticate,
  [
    body('tokenAmount').isNumeric().isFloat({ min: 1 }).withMessage('Token amount must be positive'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { tokenAmount } = req.body;

      const tokenToKesRate = paymentIntegrationService.getTokenToKesRate();
      const platformFee = paymentIntegrationService.calculatePlatformFee(tokenAmount, tokenToKesRate);

      res.json({
        success: true,
        data: {
          tokenAmount,
          tokenToKesRate,
          platformFee,
          totalCost: tokenAmount * tokenToKesRate + platformFee,
        },
      });
    } catch (error: any) {
      console.error('Platform fee calculation error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to calculate platform fee',
      });
    }
  }
);

/**
 * Get conversion rates
 * GET /api/payment-integration/rates
 */
router.get('/rates',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const tokenToKesRate = paymentIntegrationService.getTokenToKesRate();
      const kesToTokenRate = paymentIntegrationService.getKesToTokenRate();

      res.json({
        success: true,
        data: {
          tokenToKes: tokenToKesRate,
          kesToToken: kesToTokenRate,
          platformFeePercentage: 5,
        },
      });
    } catch (error: any) {
      console.error('Rates error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get conversion rates',
      });
    }
  }
);

/**
 * Initiate STK Push for any payment
 * POST /api/payment-integration/:paymentId/stk-push
 */
router.post('/:paymentId/stk-push',
  authenticate,
  [
    param('paymentId').isString().withMessage('Invalid payment ID'),
    body('phoneNumber').matches(/^(\+254|254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { paymentId } = req.params;
      const { phoneNumber } = req.body;

      const result = await paymentService.initiateSTKPush(paymentId, phoneNumber);

      res.json({
        success: true,
        message: 'STK Push initiated successfully',
        data: result,
      });
    } catch (error: any) {
      console.error('STK Push error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to initiate STK Push',
      });
    }
  }
);

export default router;
