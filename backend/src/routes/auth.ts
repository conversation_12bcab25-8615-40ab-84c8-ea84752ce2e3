import { Router } from 'express';
import { validationResult } from 'express-validator';
import User from '@/models/User';
import { otpService } from '@/services/otp';
import { tokenService } from '@/services/token';
import { smsService } from '@/services/sms';
import { generateToken } from '@/middleware/auth';
import { authenticate, AuthenticatedRequest } from '@/middleware/auth';
import { createError } from '@/middleware/errorHandler';
import { formatPhoneNumber, calculateSustainabilityScore } from '@/utils/helpers';
import { validateUserRegistration, validateOTP } from '@/utils/validation';
import { logger } from '@/utils/logger';

const router = Router();

// User registration
router.post('/register', validateUserRegistration, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const {
      phoneNumber,
      email,
      firstName,
      lastName,
      location,
      preferences = {}
    } = req.body;

    const formattedPhone = formatPhoneNumber(phoneNumber);

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { phoneNumber: formattedPhone },
        ...(email ? [{ email }] : [])
      ]
    });

    if (existingUser) {
      if (existingUser.phoneNumber === formattedPhone) {
        return next(createError('Phone number already registered', 409));
      }
      if (email && existingUser.email === email) {
        return next(createError('Email already registered', 409));
      }
    }

    // Create new user (not verified yet)
    const user = new User({
      phoneNumber: formattedPhone,
      email,
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      location,
      preferences,
      isVerified: false,
      isActive: false, // Will be activated after OTP verification
      role: 'user',
      pediTokens: 0, // Will be awarded after verification
      rating: { average: 0, count: 0 },
      sustainabilityScore: 0,
      totalDonations: 0,
      totalSwaps: 0,
    });

    await user.save();

    // Send OTP for verification
    const otpResult = await otpService.generateAndSendOTP(formattedPhone);

    if (!otpResult.success) {
      // If OTP sending fails, we should still allow the user to try again
      logger.warn('Failed to send OTP during registration', {
        phoneNumber: formattedPhone,
        error: otpResult.message
      });
    }

    logger.info('User registered successfully', {
      userId: user._id,
      phoneNumber: formattedPhone,
      otpSent: otpResult.success
    });

    res.status(201).json({
      success: true,
      message: 'Registration successful. Please verify your phone number.',
      data: {
        userId: user._id,
        phoneNumber: formattedPhone,
        otpSent: otpResult.success
      }
    });

  } catch (error) {
    next(error);
  }
});

// Verify OTP and complete registration
router.post('/verify-otp', validateOTP, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const { phoneNumber, otp } = req.body;
    const formattedPhone = formatPhoneNumber(phoneNumber);

    // Verify OTP
    const otpResult = await otpService.verifyOTP(formattedPhone, otp);

    if (!otpResult.success) {
      return next(createError(otpResult.message, 400));
    }

    // Find and activate user
    const user = await User.findOne({ phoneNumber: formattedPhone });

    if (!user) {
      return next(createError('User not found', 404));
    }

    if (user.isVerified) {
      return next(createError('Phone number already verified', 400));
    }

    // Activate user account
    user.isVerified = true;
    user.isActive = true;
    user.lastActive = new Date();

    await user.save();

    // Award welcome tokens
    const welcomeTokens = await tokenService.earnTokens({
      userId: user._id.toString(),
      action: 'welcome',
      description: 'Welcome to Pedi! Start your sustainable fashion journey.',
      metadata: {
        isFirstTime: true,
        registrationDate: new Date()
      }
    });



    // Send welcome SMS
    await smsService.sendWelcomeMessage(formattedPhone, user.firstName);

    // Send welcome notification
    try {
      const { notificationIntegrationService } = await import('@/services/notificationIntegration');
      await notificationIntegrationService.sendWelcomeNotification(user._id.toString(), user.firstName);
    } catch (notificationError) {
      logger.error('Failed to send welcome notification:', notificationError);
    }

    // Generate JWT token
    const token = generateToken(user._id.toString());

    logger.info('User verification completed', {
      userId: user._id,
      phoneNumber: formattedPhone
    });

    res.json({
      success: true,
      message: welcomeTokens.success
        ? `Phone number verified successfully. Welcome to Pedi! You've earned ${welcomeTokens.tokensEarned || 100} welcome tokens.`
        : 'Phone number verified successfully. Welcome to Pedi!',
      data: {
        token,
        user: {
          id: user._id,
          phoneNumber: user.phoneNumber,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          location: user.location,
          isVerified: user.isVerified,
          role: user.role,
          pediTokens: welcomeTokens.newBalance || user.pediTokens,
          rating: user.rating,
          sustainabilityScore: user.sustainabilityScore,
          joinedAt: user.createdAt
        },
        welcomeBonus: welcomeTokens.success ? {
          tokensEarned: welcomeTokens.tokensEarned || 100,
          newBalance: welcomeTokens.newBalance
        } : null
      }
    });

  } catch (error) {
    next(error);
  }
});

// Login (for existing users)
router.post('/login', validateOTP, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const { phoneNumber, otp } = req.body;
    const formattedPhone = formatPhoneNumber(phoneNumber);

    // Verify OTP
    const otpResult = await otpService.verifyOTP(formattedPhone, otp);

    if (!otpResult.success) {
      return next(createError(otpResult.message, 400));
    }

    // Find user
    const user = await User.findOne({ phoneNumber: formattedPhone });

    if (!user) {
      return next(createError('User not found. Please register first.', 404));
    }

    if (!user.isVerified) {
      return next(createError('Phone number not verified', 400));
    }

    if (!user.isActive) {
      return next(createError('Account is deactivated', 401));
    }

    // Update last active
    user.lastActive = new Date();
    await user.save();

    // Process daily login bonus
    await tokenService.processDailyLoginBonus(user._id.toString());

    // Generate JWT token
    const token = generateToken(user._id.toString());

    logger.info('User logged in successfully', {
      userId: user._id,
      phoneNumber: formattedPhone
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: {
          id: user._id,
          phoneNumber: user.phoneNumber,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          profilePicture: user.profilePicture,
          location: user.location,
          isVerified: user.isVerified,
          role: user.role,
          pediTokens: user.pediTokens,
          rating: user.rating,
          sustainabilityScore: user.sustainabilityScore,
          totalDonations: user.totalDonations,
          totalSwaps: user.totalSwaps,
          joinedAt: user.createdAt,
          lastActive: user.lastActive
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Request OTP (for login or registration)
router.post('/request-otp', async (req, res, next) => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      return next(createError('Phone number is required', 400));
    }

    const formattedPhone = formatPhoneNumber(phoneNumber);

    // Validate phone number format
    if (!/^(\+254|0)[17]\d{8}$/.test(phoneNumber)) {
      return next(createError('Please enter a valid Kenyan phone number', 400));
    }

    // Check if user exists
    const user = await User.findOne({ phoneNumber: formattedPhone });
    const isNewUser = !user;

    // Send OTP
    const otpResult = await otpService.generateAndSendOTP(formattedPhone);

    if (!otpResult.success) {
      return next(createError(otpResult.message, 500));
    }

    logger.info('OTP requested', {
      phoneNumber: formattedPhone,
      isNewUser,
      messageId: 'SMS sent'
    });

    res.json({
      success: true,
      message: 'OTP sent successfully',
      data: {
        phoneNumber: formattedPhone,
        isNewUser,
        expiresIn: 600 // 10 minutes
      }
    });

  } catch (error) {
    next(error);
  }
});

// Logout
router.post('/logout', authenticate, async (req, res, next) => {
  try {
    // In a stateless JWT system, logout is mainly client-side
    // But we can update the user's last active time
    const user = (req as AuthenticatedRequest).user;
    user.lastActive = new Date();
    await user.save();

    logger.info('User logged out', {
      userId: user._id,
      phoneNumber: user.phoneNumber
    });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    next(error);
  }
});

// Get current user profile
router.get('/me', authenticate, async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user;

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          phoneNumber: user.phoneNumber,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          profilePicture: user.profilePicture,
          location: user.location,
          isVerified: user.isVerified,
          role: user.role,
          pediTokens: user.pediTokens,
          rating: user.rating,
          sustainabilityScore: user.sustainabilityScore,
          totalDonations: user.totalDonations,
          totalSwaps: user.totalSwaps,
          preferences: user.preferences,
          joinedAt: user.createdAt,
          lastActive: user.lastActive
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

export default router;
