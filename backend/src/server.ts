import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server, Socket } from 'socket.io';
import dotenv from 'dotenv';

// Extend the Socket interface to include custom properties
declare module 'socket.io' {
  interface Socket {
    userId: string;
    user: any;
  }
}

import { connectDatabase } from '@/config/database';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { setSocketInstance } from '@/services/socket';
import { socketAuthMiddleware } from '@/middleware/socketAuth';

// Import routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import clothingRoutes from '@/routes/clothing';
import transactionRoutes from '@/routes/transactions';
import tokenRoutes from '@/routes/tokens';
import chatRoutes from '@/routes/chat';
import achievementRoutes from '@/routes/achievements';
import exchangeRoutes from '@/routes/exchanges';
import exchangePreferencesRoutes from '@/routes/exchangePreferences';
import swapRoutes from '@/routes/swaps';
import tokenPurchaseRoutes from '@/routes/tokenPurchases';
import donationRoutes from '@/routes/donations';
import charityPartnerRoutes from '@/routes/charityPartners';
import paymentRoutes from '@/routes/payments';
import aiRoutes from '@/routes/ai';
import paymentMethodRoutes from '@/routes/paymentMethods';
import paymentIntegrationRoutes from './routes/paymentIntegration';
import notificationRoutes from '@/routes/notifications';

// Import new routes for enhanced features
import bulkDonationRoutes from '../routes/bulkDonations';
import recyclingCenterRoutes from '../routes/recyclingCenters';
import recyclingRoutes from '../routes/recycling';
import tokenPurchaseRoutesNew from '../routes/tokenPurchases';
import tokenPurchaseAPI from '@/routes/tokenPurchaseAPI';
import adminRoutes from '../routes/admin';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
    methods: ['GET', 'POST'],
  },
});

// Set the socket instance for use in other modules
setSocketInstance(io);

const PORT = process.env['PORT'] || 5000;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000'), // 15 minutes
  max: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
  credentials: true,
}));
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env['NODE_ENV'] || 'development',
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/clothing', clothingRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/tokens', tokenRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/achievements', achievementRoutes);
app.use('/api/exchanges', exchangeRoutes);
app.use('/api/exchange-preferences', exchangePreferencesRoutes);
app.use('/api/swaps', swapRoutes);
app.use('/api/token-purchases', tokenPurchaseRoutes);
app.use('/api/donations', donationRoutes);
app.use('/api/charity-partners', charityPartnerRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/payment-methods', paymentMethodRoutes);
app.use('/api/payment-integration', paymentIntegrationRoutes);
app.use('/api/notifications', notificationRoutes);

// Enhanced feature routes
app.use('/api/bulk-donations', bulkDonationRoutes);
app.use('/api/recycling-centers', recyclingCenterRoutes);
app.use('/api/recycling', recyclingRoutes);
app.use('/api/token-purchases-new', tokenPurchaseRoutesNew);
app.use('/api/token-purchase', tokenPurchaseAPI);
app.use('/api/admin', adminRoutes);

// Socket.IO for real-time chat with enhanced features
io.use(socketAuthMiddleware);

// Track online users
const onlineUsers = new Map();

io.on('connection', (socket) => {
  const userId = socket.userId;
  const user = socket.user;

  logger.info(`User connected: ${user.firstName} ${user.lastName} (${socket.id})`);

  // Add user to online users
  onlineUsers.set(userId, {
    socketId: socket.id,
    user: {
      id: userId,
      firstName: user.firstName,
      lastName: user.lastName,
      profilePicture: user.profilePicture,
    },
    lastSeen: new Date(),
  });

  // Broadcast user online status
  socket.broadcast.emit('user-online', {
    userId,
    user: {
      id: userId,
      firstName: user.firstName,
      lastName: user.lastName,
      profilePicture: user.profilePicture,
    },
  });

  // Join user to their personal room for notifications
  socket.join(`user:${userId}`);

  // Handle joining chat rooms
  socket.on('join-chat', (chatId: string) => {
    socket.join(`chat:${chatId}`);
    logger.info(`User ${user.firstName} joined chat ${chatId}`);

    // Notify other participants that user joined
    socket.to(`chat:${chatId}`).emit('user-joined-chat', {
      chatId,
      user: {
        id: userId,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.profilePicture,
      },
    });
  });

  // Handle leaving chat rooms
  socket.on('leave-chat', (chatId: string) => {
    socket.leave(`chat:${chatId}`);
    logger.info(`User ${user.firstName} left chat ${chatId}`);

    // Notify other participants that user left
    socket.to(`chat:${chatId}`).emit('user-left-chat', {
      chatId,
      userId,
    });
  });

  // Handle typing indicators
  socket.on('typing-start', (data: { chatId: string }) => {
    socket.to(`chat:${data.chatId}`).emit('user-typing', {
      chatId: data.chatId,
      userId,
      user: {
        firstName: user.firstName,
        lastName: user.lastName,
      },
    });
  });

  socket.on('typing-stop', (data: { chatId: string }) => {
    socket.to(`chat:${data.chatId}`).emit('user-stopped-typing', {
      chatId: data.chatId,
      userId,
    });
  });

  // Handle message delivery confirmations
  socket.on('message-delivered', (data: { chatId: string; messageId: string }) => {
    socket.to(`chat:${data.chatId}`).emit('message-delivery-confirmed', {
      chatId: data.chatId,
      messageId: data.messageId,
      deliveredTo: userId,
    });
  });

  // Handle presence updates
  socket.on('update-presence', () => {
    if (onlineUsers.has(userId)) {
      onlineUsers.get(userId).lastSeen = new Date();
    }
  });

  // Handle getting online users
  socket.on('get-online-users', () => {
    const onlineUsersList = Array.from(onlineUsers.values()).map(userData => userData.user);
    socket.emit('online-users-list', onlineUsersList);
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    logger.info(`User disconnected: ${user.firstName} ${user.lastName} (${socket.id})`);

    // Remove from online users
    onlineUsers.delete(userId);

    // Broadcast user offline status
    socket.broadcast.emit('user-offline', {
      userId,
      lastSeen: new Date(),
    });
  });
});

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    // Connect to database
    await connectDatabase();
    
    server.listen(PORT, () => {
      logger.info(`🚀 Pedi Backend Server running on port ${PORT}`);
      logger.info(`📱 Environment: ${process.env['NODE_ENV'] || 'development'}`);
      logger.info(`🌍 Frontend URL: ${process.env['FRONTEND_URL'] || 'http://localhost:3000'}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  logger.error('Unhandled Promise Rejection:', err);
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    logger.info('Process terminated');
  });
});

startServer();

export { app, io };
