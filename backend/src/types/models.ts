import { Model } from 'mongoose';
import { INotificationPreferences } from '../models/NotificationPreferences';
import { INotificationTemplate } from '../models/NotificationTemplate';

// Extended interfaces for models with static methods
export interface INotificationPreferencesModel extends Model<INotificationPreferences> {
  getOrCreateForUser(userId: string): Promise<INotificationPreferences>;
  updateChannelInfo(userId: string, channel: string, info: Record<string, any>): Promise<INotificationPreferences | null>;
}

export interface INotificationTemplateModel extends Model<INotificationTemplate> {
  findByCategory(category: string, type?: string): Promise<INotificationTemplate[]>;
  findActiveTemplates(): Promise<INotificationTemplate[]>;
  getTemplateStats(): Promise<any[]>;
}
