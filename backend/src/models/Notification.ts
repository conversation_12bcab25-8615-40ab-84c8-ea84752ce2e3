import mongoose, { Document, Schema } from 'mongoose';

export interface INotification extends Document {
  _id: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  type: 'sms' | 'push' | 'email' | 'in_app';
  category: 'payment' | 'exchange' | 'system' | 'promotion' | 'reminder' | 'welcome' | 'token' | 'security';
  title: string;
  message: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';

  // SMS specific fields
  phoneNumber?: string;
  messageId?: string;
  cost?: string;

  // Scheduling
  scheduledAt?: Date;
  sentAt?: Date;
  deliveredAt?: Date;

  // Metadata
  relatedEntity?: {
    type: 'payment' | 'transaction' | 'exchange' | 'user' | 'item';
    id: string;
  };
  templateId?: mongoose.Types.ObjectId;
  templateVariables?: Record<string, any>;

  // Tracking
  attempts: number;
  maxAttempts: number;
  lastAttemptAt?: Date;
  failureReason?: string;

  // Audit
  createdAt: Date;
  updatedAt: Date;
  createdBy?: mongoose.Types.ObjectId;

  // Virtual properties
  canRetry: boolean;
  isOverdue: boolean;

  // Instance methods
  markAsSent(messageId?: string, cost?: string): Promise<INotification>;
  markAsDelivered(): Promise<INotification>;
  markAsFailed(reason: string): Promise<INotification>;
  retry(): Promise<INotification>;
}

const notificationSchema = new Schema<INotification>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  type: {
    type: String,
    enum: ['sms', 'push', 'email', 'in_app'],
    required: true,
    default: 'sms',
  },
  category: {
    type: String,
    enum: ['payment', 'exchange', 'system', 'promotion', 'reminder', 'welcome', 'token', 'security'],
    required: true,
    index: true,
  },
  title: {
    type: String,
    required: true,
    maxlength: 100,
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'failed', 'cancelled'],
    default: 'pending',
    index: true,
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true,
  },
  
  // SMS specific fields
  phoneNumber: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^(\+254|254)[17]\d{8}$/.test(v);
      },
      message: 'Invalid phone number format',
    },
  },
  messageId: {
    type: String,
    sparse: true,
    index: true,
  },
  cost: {
    type: String,
  },
  
  // Scheduling
  scheduledAt: {
    type: Date,
    index: true,
  },
  sentAt: {
    type: Date,
    index: true,
  },
  deliveredAt: {
    type: Date,
  },
  
  // Metadata
  relatedEntity: {
    type: {
      type: String,
      enum: ['payment', 'transaction', 'exchange', 'user', 'item'],
    },
    id: String,
  },
  templateId: {
    type: Schema.Types.ObjectId,
    ref: 'NotificationTemplate',
  },
  templateVariables: {
    type: Schema.Types.Mixed,
  },
  
  // Tracking
  attempts: {
    type: Number,
    default: 0,
    min: 0,
  },
  maxAttempts: {
    type: Number,
    default: 3,
    min: 1,
    max: 10,
  },
  lastAttemptAt: {
    type: Date,
  },
  failureReason: {
    type: String,
    maxlength: 500,
  },
  
  // Audit
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
});

// Indexes for performance
notificationSchema.index({ user: 1, status: 1 });
notificationSchema.index({ user: 1, category: 1 });
notificationSchema.index({ user: 1, createdAt: -1 });
notificationSchema.index({ status: 1, scheduledAt: 1 });
notificationSchema.index({ status: 1, priority: 1, createdAt: 1 });
notificationSchema.index({ messageId: 1 }, { sparse: true });
notificationSchema.index({ 'relatedEntity.type': 1, 'relatedEntity.id': 1 });

// Compound index for efficient querying
notificationSchema.index({ 
  user: 1, 
  type: 1, 
  status: 1, 
  createdAt: -1 
});

// TTL index to automatically delete old notifications (90 days)
notificationSchema.index({ createdAt: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });

// Virtual for checking if notification is overdue
notificationSchema.virtual('isOverdue').get(function() {
  if (!this.scheduledAt || this.status !== 'pending') return false;
  return new Date() > this.scheduledAt;
});

// Virtual for checking if notification can be retried
notificationSchema.virtual('canRetry').get(function() {
  return this.status === 'failed' && this.attempts < this.maxAttempts;
});

// Pre-save middleware
notificationSchema.pre('save', function(next) {
  // Set phone number from user if not provided and type is SMS
  if (this.type === 'sms' && !this.phoneNumber && this.isNew) {
    // This would need to be populated from the user document
    // We'll handle this in the service layer
  }
  
  // Auto-schedule immediate notifications
  if (!this.scheduledAt && this.isNew) {
    this.scheduledAt = new Date();
  }
  
  next();
});

// Instance methods
notificationSchema.methods.markAsSent = function(messageId?: string, cost?: string) {
  this.status = 'sent';
  this.sentAt = new Date();
  this.attempts += 1;
  this.lastAttemptAt = new Date();
  
  if (messageId) this.messageId = messageId;
  if (cost) this.cost = cost;
  
  return this.save();
};

notificationSchema.methods.markAsDelivered = function() {
  this.status = 'delivered';
  this.deliveredAt = new Date();
  
  return this.save();
};

notificationSchema.methods.markAsFailed = function(reason: string) {
  this.status = 'failed';
  this.attempts += 1;
  this.lastAttemptAt = new Date();
  this.failureReason = reason;
  
  return this.save();
};

notificationSchema.methods.retry = function() {
  if (!this.canRetry) {
    throw new Error('Notification cannot be retried');
  }
  
  this.status = 'pending';
  this.failureReason = undefined;
  
  return this.save();
};

// Static methods
notificationSchema.statics.findPendingNotifications = function(limit = 100) {
  return this.find({
    status: 'pending',
    scheduledAt: { $lte: new Date() },
    attempts: { $lt: this.schema.paths.maxAttempts.options.default },
  })
  .sort({ priority: -1, scheduledAt: 1 })
  .limit(limit)
  .populate('user', 'phoneNumber firstName lastName')
  .populate('templateId');
};

notificationSchema.statics.findByUser = function(userId: string, options: {
  type?: string;
  category?: string;
  status?: string;
  limit?: number;
  skip?: number;
} = {}) {
  const query: any = { user: userId };
  
  if (options.type) query.type = options.type;
  if (options.category) query.category = options.category;
  if (options.status) query.status = options.status;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0)
    .populate('templateId');
};

notificationSchema.statics.getNotificationStats = function(userId?: string) {
  const matchStage: any = {};
  if (userId) matchStage.user = new mongoose.Types.ObjectId(userId);
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: {
          status: '$status',
          type: '$type',
          category: '$category',
        },
        count: { $sum: 1 },
        totalCost: { 
          $sum: { 
            $toDouble: { 
              $ifNull: ['$cost', '0'] 
            } 
          } 
        },
      },
    },
    {
      $group: {
        _id: null,
        stats: {
          $push: {
            status: '$_id.status',
            type: '$_id.type',
            category: '$_id.category',
            count: '$count',
            totalCost: '$totalCost',
          },
        },
        totalNotifications: { $sum: '$count' },
        totalCost: { $sum: '$totalCost' },
      },
    },
  ]);
};

// Interface for static methods
export interface INotificationModel extends mongoose.Model<INotification> {
  findPendingNotifications(limit?: number): mongoose.Query<INotification[], INotification>;
  findByUser(userId: string, options?: {
    type?: string;
    category?: string;
    status?: string;
    limit?: number;
    skip?: number;
  }): mongoose.Query<INotification[], INotification>;
  getNotificationStats(userId?: string): mongoose.Aggregate<any[]>;
}

const Notification = mongoose.model<INotification, INotificationModel>('Notification', notificationSchema);

export default Notification;
