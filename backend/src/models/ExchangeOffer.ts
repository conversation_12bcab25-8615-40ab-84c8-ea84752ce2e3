import mongoose, { Document, Schema } from 'mongoose';

export interface IExchangeOffer extends Document {
  _id: mongoose.Types.ObjectId;
  type: 'swap' | 'token_purchase' | 'donation';
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'withdrawn';
  
  // Participants
  offerer: mongoose.Types.ObjectId;
  targetUser: mongoose.Types.ObjectId; // Owner of the item being requested
  
  // Items involved
  requestedItems: mongoose.Types.ObjectId[]; // Items the offerer wants
  offeredItems?: mongoose.Types.ObjectId[]; // Items the offerer is giving (for swaps)
  tokenAmount?: number; // For token purchases
  
  // Offer details
  message?: string; // Personal message from offerer
  conditions?: string; // Special conditions or requirements
  
  // Negotiation
  counterOffers: {
    offeredBy: mongoose.Types.ObjectId;
    tokenAmount?: number;
    offeredItems?: mongoose.Types.ObjectId[];
    message?: string;
    createdAt: Date;
  }[];
  
  // Delivery preferences
  preferredDeliveryMethod: 'pickup' | 'delivery' | 'meetup';
  deliveryNotes?: string;
  
  // Timing
  expiresAt: Date;
  respondedAt?: Date;
  
  // Quality assurance (for donations)
  qualityCheck?: {
    required: boolean;
    notes?: string;
    scheduledAt?: Date;
  };
  
  // Charity information (for donations)
  charityPartner?: {
    name: string;
    id: mongoose.Types.ObjectId;
    category: string;
    impactDescription?: string;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

const exchangeOfferSchema = new Schema<IExchangeOffer>({
  type: {
    type: String,
    required: true,
    enum: ['swap', 'token_purchase', 'donation'],
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'accepted', 'declined', 'expired', 'withdrawn'],
    default: 'pending',
  },
  offerer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  targetUser: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  requestedItems: [{
    type: Schema.Types.ObjectId,
    ref: 'ClothingItem',
    required: true,
  }],
  offeredItems: [{
    type: Schema.Types.ObjectId,
    ref: 'ClothingItem',
  }],
  tokenAmount: {
    type: Number,
    min: 0,
  },
  message: {
    type: String,
    maxlength: 500,
    trim: true,
  },
  conditions: {
    type: String,
    maxlength: 300,
    trim: true,
  },
  counterOffers: [{
    offeredBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    tokenAmount: {
      type: Number,
      min: 0,
    },
    offeredItems: [{
      type: Schema.Types.ObjectId,
      ref: 'ClothingItem',
    }],
    message: {
      type: String,
      maxlength: 300,
      trim: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  preferredDeliveryMethod: {
    type: String,
    required: true,
    enum: ['pickup', 'delivery', 'meetup'],
  },
  deliveryNotes: {
    type: String,
    maxlength: 200,
    trim: true,
  },
  expiresAt: {
    type: Date,
    required: true,
    default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
  },
  respondedAt: {
    type: Date,
  },
  qualityCheck: {
    required: {
      type: Boolean,
      default: false,
    },
    notes: {
      type: String,
      maxlength: 200,
      trim: true,
    },
    scheduledAt: Date,
  },
  charityPartner: {
    name: {
      type: String,
      trim: true,
    },
    id: {
      type: Schema.Types.ObjectId,
      ref: 'CharityPartner',
    },
    category: {
      type: String,
      trim: true,
    },
    impactDescription: {
      type: String,
      maxlength: 300,
      trim: true,
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
exchangeOfferSchema.index({ offerer: 1 });
exchangeOfferSchema.index({ targetUser: 1 });
exchangeOfferSchema.index({ type: 1 });
exchangeOfferSchema.index({ status: 1 });
exchangeOfferSchema.index({ expiresAt: 1 });
exchangeOfferSchema.index({ createdAt: -1 });
exchangeOfferSchema.index({ requestedItems: 1 });

// Compound indexes for efficient queries
exchangeOfferSchema.index({ targetUser: 1, status: 1 });
exchangeOfferSchema.index({ offerer: 1, status: 1 });
exchangeOfferSchema.index({ type: 1, status: 1 });

// Virtual for checking if offer is expired
exchangeOfferSchema.virtual('isExpired').get(function() {
  return this.status === 'pending' && new Date() > this.expiresAt;
});

// Virtual for time remaining
exchangeOfferSchema.virtual('timeRemaining').get(function() {
  if (this.status !== 'pending') return null;
  const now = new Date();
  const remaining = this.expiresAt.getTime() - now.getTime();
  return remaining > 0 ? remaining : 0;
});

// Virtual for latest counter offer
exchangeOfferSchema.virtual('latestCounterOffer').get(function() {
  if (this.counterOffers.length === 0) return null;
  return this.counterOffers[this.counterOffers.length - 1];
});

// Pre-save middleware to handle status changes
exchangeOfferSchema.pre('save', function(next) {
  // Auto-expire if past expiration date
  if (this.status === 'pending' && new Date() > this.expiresAt) {
    this.status = 'expired';
  }
  
  // Set respondedAt when status changes from pending
  if (this.isModified('status') && this.status !== 'pending' && !this.respondedAt) {
    this.respondedAt = new Date();
  }
  
  next();
});

// Static method to find expired offers
exchangeOfferSchema.statics.findExpired = function() {
  return this.find({
    status: 'pending',
    expiresAt: { $lt: new Date() }
  });
};

// Static method to auto-expire offers
exchangeOfferSchema.statics.expireOldOffers = async function() {
  const result = await this.updateMany(
    {
      status: 'pending',
      expiresAt: { $lt: new Date() }
    },
    {
      $set: { status: 'expired' }
    }
  );
  return result;
};

// Interface for static methods
interface IExchangeOfferModel extends mongoose.Model<IExchangeOffer> {
  findExpired(): mongoose.Query<IExchangeOffer[], IExchangeOffer>;
  expireOldOffers(): Promise<any>;
}

export default mongoose.model<IExchangeOffer, IExchangeOfferModel>('ExchangeOffer', exchangeOfferSchema);
