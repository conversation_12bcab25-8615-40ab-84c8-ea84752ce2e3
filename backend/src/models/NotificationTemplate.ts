import mongoose, { Document, Schema } from 'mongoose';

export interface INotificationTemplate extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  category: 'payment' | 'exchange' | 'system' | 'promotion' | 'reminder' | 'welcome' | 'token' | 'security';
  type: 'sms' | 'push' | 'email' | 'in_app';

  // Template content
  title: string;
  message: string;
  variables: Array<{
    name: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    required: boolean;
    description: string;
    defaultValue?: any;
  }>;

  // Configuration
  isActive: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  maxAttempts: number;
  retryDelay: number; // in minutes

  // Scheduling
  canSchedule: boolean;
  defaultScheduleDelay?: number; // in minutes

  // Targeting
  targetAudience: 'all' | 'verified' | 'active' | 'premium' | 'custom';
  customAudienceFilter?: Record<string, any>;

  // Limits
  dailyLimit?: number;
  monthlyLimit?: number;

  // Audit
  createdAt: Date;
  updatedAt: Date;
  createdBy: mongoose.Types.ObjectId;
  lastUsedAt?: Date;
  usageCount: number;

  // Virtual properties
  hasLimits: boolean;

  // Instance methods
  renderMessage(variables?: Record<string, any>): { title: string; message: string };
  validateVariables(variables: Record<string, any>): { isValid: boolean; errors: string[] };
  incrementUsage(): Promise<INotificationTemplate>;
  checkLimits(userId?: string): Promise<{ canSend: boolean; reason?: string }>;
}

const notificationTemplateSchema = new Schema<INotificationTemplate>({
  name: {
    type: String,
    required: true,
    unique: true,
    maxlength: 100,
    index: true,
  },
  description: {
    type: String,
    required: true,
    maxlength: 500,
  },
  category: {
    type: String,
    enum: ['payment', 'exchange', 'system', 'promotion', 'reminder', 'welcome', 'token', 'security'],
    required: true,
    index: true,
  },
  type: {
    type: String,
    enum: ['sms', 'push', 'email', 'in_app'],
    required: true,
    index: true,
  },
  
  // Template content
  title: {
    type: String,
    required: true,
    maxlength: 100,
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000,
    validate: {
      validator: function(v: string) {
        // For SMS, ensure message length is reasonable
        if (this.type === 'sms') {
          // After variable substitution, should not exceed 160 chars for single SMS
          return v.length <= 160;
        }
        return true;
      },
      message: 'SMS message template too long',
    },
  },
  variables: [{
    name: {
      type: String,
      required: true,
      validate: {
        validator: function(v: string) {
          return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(v);
        },
        message: 'Variable name must be a valid identifier',
      },
    },
    type: {
      type: String,
      enum: ['string', 'number', 'date', 'boolean'],
      required: true,
    },
    required: {
      type: Boolean,
      default: false,
    },
    description: {
      type: String,
      required: true,
      maxlength: 200,
    },
    defaultValue: Schema.Types.Mixed,
  }],
  
  // Configuration
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
  },
  maxAttempts: {
    type: Number,
    default: 3,
    min: 1,
    max: 10,
  },
  retryDelay: {
    type: Number,
    default: 5, // 5 minutes
    min: 1,
    max: 1440, // 24 hours
  },
  
  // Scheduling
  canSchedule: {
    type: Boolean,
    default: false,
  },
  defaultScheduleDelay: {
    type: Number,
    min: 0,
    max: 10080, // 1 week in minutes
  },
  
  // Targeting
  targetAudience: {
    type: String,
    enum: ['all', 'verified', 'active', 'premium', 'custom'],
    default: 'verified',
  },
  customAudienceFilter: {
    type: Schema.Types.Mixed,
  },
  
  // Limits
  dailyLimit: {
    type: Number,
    min: 1,
  },
  monthlyLimit: {
    type: Number,
    min: 1,
  },
  
  // Audit
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  lastUsedAt: {
    type: Date,
  },
  usageCount: {
    type: Number,
    default: 0,
    min: 0,
  },
}, {
  timestamps: true,
});

// Indexes
notificationTemplateSchema.index({ category: 1, type: 1, isActive: 1 });
notificationTemplateSchema.index({ name: 1 }, { unique: true });
notificationTemplateSchema.index({ isActive: 1, category: 1 });

// Virtual for checking if template has usage limits
notificationTemplateSchema.virtual('hasLimits').get(function() {
  return !!(this.dailyLimit || this.monthlyLimit);
});

// Instance methods
notificationTemplateSchema.methods.renderMessage = function(variables: Record<string, any> = {}) {
  let renderedTitle = this.title;
  let renderedMessage = this.message;
  
  // Replace variables in title and message
  for (const variable of this.variables) {
    const value = variables[variable.name] ?? variable.defaultValue ?? '';
    const placeholder = new RegExp(`{{\\s*${variable.name}\\s*}}`, 'g');
    
    // Format value based on type
    let formattedValue = value;
    if (variable.type === 'date' && value instanceof Date) {
      formattedValue = value.toLocaleDateString();
    } else if (variable.type === 'number' && typeof value === 'number') {
      formattedValue = value.toLocaleString();
    }
    
    renderedTitle = renderedTitle.replace(placeholder, String(formattedValue));
    renderedMessage = renderedMessage.replace(placeholder, String(formattedValue));
  }
  
  return {
    title: renderedTitle,
    message: renderedMessage,
  };
};

notificationTemplateSchema.methods.validateVariables = function(variables: Record<string, any>) {
  const errors: string[] = [];
  
  for (const variable of this.variables) {
    const value = variables[variable.name];
    
    // Check required variables
    if (variable.required && (value === undefined || value === null || value === '')) {
      errors.push(`Variable '${variable.name}' is required`);
      continue;
    }
    
    // Type validation
    if (value !== undefined && value !== null) {
      switch (variable.type) {
        case 'number':
          if (typeof value !== 'number' && isNaN(Number(value))) {
            errors.push(`Variable '${variable.name}' must be a number`);
          }
          break;
        case 'boolean':
          if (typeof value !== 'boolean') {
            errors.push(`Variable '${variable.name}' must be a boolean`);
          }
          break;
        case 'date':
          if (!(value instanceof Date) && isNaN(Date.parse(value))) {
            errors.push(`Variable '${variable.name}' must be a valid date`);
          }
          break;
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

notificationTemplateSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsedAt = new Date();
  return this.save();
};

notificationTemplateSchema.methods.checkLimits = async function(userId?: string) {
  if (!this.hasLimits) return { canSend: true };
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  
  // Import here to avoid circular dependency
  const Notification = mongoose.model('Notification');
  
  const query: any = { templateId: this._id };
  if (userId) query.user = userId;
  
  // Check daily limit
  if (this.dailyLimit) {
    const todayCount = await Notification.countDocuments({
      ...query,
      createdAt: { $gte: today },
    });
    
    if (todayCount >= this.dailyLimit) {
      return { 
        canSend: false, 
        reason: `Daily limit of ${this.dailyLimit} notifications reached` 
      };
    }
  }
  
  // Check monthly limit
  if (this.monthlyLimit) {
    const monthCount = await Notification.countDocuments({
      ...query,
      createdAt: { $gte: thisMonth },
    });
    
    if (monthCount >= this.monthlyLimit) {
      return { 
        canSend: false, 
        reason: `Monthly limit of ${this.monthlyLimit} notifications reached` 
      };
    }
  }
  
  return { canSend: true };
};

// Static methods
notificationTemplateSchema.statics.findByCategory = function(category: string, type?: string) {
  const query: any = { category, isActive: true };
  if (type) query.type = type;
  
  return this.find(query).sort({ name: 1 });
};

notificationTemplateSchema.statics.findActiveTemplates = function() {
  return this.find({ isActive: true }).sort({ category: 1, name: 1 });
};

notificationTemplateSchema.statics.getTemplateStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: {
          category: '$category',
          type: '$type',
          isActive: '$isActive',
        },
        count: { $sum: 1 },
        totalUsage: { $sum: '$usageCount' },
        avgUsage: { $avg: '$usageCount' },
      },
    },
    {
      $sort: { '_id.category': 1, '_id.type': 1 },
    },
  ]);
};

// Interface for static methods
export interface INotificationTemplateModel extends mongoose.Model<INotificationTemplate> {
  findByCategory(category: string, type?: string): mongoose.Query<INotificationTemplate[], INotificationTemplate>;
  findActiveTemplates(): mongoose.Query<INotificationTemplate[], INotificationTemplate>;
  getTemplateStats(): mongoose.Aggregate<any[]>;
}

const NotificationTemplate = mongoose.model<INotificationTemplate, INotificationTemplateModel>('NotificationTemplate', notificationTemplateSchema);

export default NotificationTemplate;
