import mongoose, { Document, Schema } from 'mongoose';

export interface IPayment extends Document {
  _id: mongoose.Types.ObjectId;
  
  // Payment identification
  paymentId: string; // Unique payment identifier
  merchantRequestId?: string; // M-Pesa merchant request ID
  checkoutRequestId?: string; // M-Pesa checkout request ID
  mpesaReceiptNumber?: string; // M-Pesa receipt number
  
  // Payment details
  amount: number;
  currency: string;
  description: string;
  
  // User and transaction references
  user: mongoose.Types.ObjectId;
  relatedTransaction?: mongoose.Types.ObjectId; // Reference to Transaction model
  relatedEntity?: {
    type: 'token_purchase' | 'platform_fee' | 'premium_feature' | 'delivery_fee';
    id: mongoose.Types.ObjectId;
  };
  
  // Payment method and provider
  paymentMethod: 'mpesa' | 'card' | 'bank_transfer';
  provider: 'safaricom' | 'visa' | 'mastercard' | 'bank';
  
  // Payment status and lifecycle
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  
  // M-Pesa specific details
  mpesaDetails?: {
    phoneNumber: string;
    accountReference: string;
    transactionType: 'STK_PUSH' | 'C2B' | 'B2C';
    resultCode?: number;
    resultDescription?: string;
    transactionDate?: Date;
  };
  
  // Callback and webhook data
  callbackData?: any;
  webhookReceived: boolean;
  webhookReceivedAt?: Date;
  
  // Retry and failure handling
  retryCount: number;
  maxRetries: number;
  lastRetryAt?: Date;
  failureReason?: string;
  
  // Refund information
  refund?: {
    amount: number;
    reason: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    refundedAt?: Date;
    refundTransactionId?: string;
  };
  
  // Metadata and tracking
  metadata?: {
    [key: string]: any;
  };
  
  // Timeline for payment status changes
  statusHistory: {
    status: string;
    timestamp: Date;
    note?: string;
    source: 'system' | 'webhook' | 'manual' | 'retry';
  }[];
  
  createdAt: Date;
  updatedAt: Date;

  // Virtual properties
  ageInMinutes: number;
}

const paymentSchema = new Schema<IPayment>({
  paymentId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  merchantRequestId: {
    type: String,
    sparse: true,
    index: true,
  },
  checkoutRequestId: {
    type: String,
    sparse: true,
    index: true,
  },
  mpesaReceiptNumber: {
    type: String,
    sparse: true,
    index: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 1,
  },
  currency: {
    type: String,
    required: true,
    default: 'KES',
    enum: ['KES', 'USD', 'EUR'],
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  relatedTransaction: {
    type: Schema.Types.ObjectId,
    ref: 'Transaction',
    sparse: true,
    index: true,
  },
  relatedEntity: {
    type: {
      type: String,
      enum: ['token_purchase', 'platform_fee', 'premium_feature', 'delivery_fee'],
    },
    id: {
      type: Schema.Types.ObjectId,
    },
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: ['mpesa', 'card', 'bank_transfer'],
    default: 'mpesa',
  },
  provider: {
    type: String,
    required: true,
    enum: ['safaricom', 'visa', 'mastercard', 'bank'],
    default: 'safaricom',
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending',
    index: true,
  },
  mpesaDetails: {
    phoneNumber: {
      type: String,
      match: [/^254[17]\d{8}$/, 'Please enter a valid Kenyan phone number'],
    },
    accountReference: {
      type: String,
      trim: true,
    },
    transactionType: {
      type: String,
      enum: ['STK_PUSH', 'C2B', 'B2C'],
    },
    resultCode: Number,
    resultDescription: String,
    transactionDate: Date,
  },
  callbackData: {
    type: Schema.Types.Mixed,
  },
  webhookReceived: {
    type: Boolean,
    default: false,
    index: true,
  },
  webhookReceivedAt: Date,
  retryCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  maxRetries: {
    type: Number,
    default: 3,
    min: 0,
  },
  lastRetryAt: Date,
  failureReason: {
    type: String,
    trim: true,
  },
  refund: {
    amount: {
      type: Number,
      min: 0,
    },
    reason: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
    },
    refundedAt: Date,
    refundTransactionId: String,
  },
  metadata: {
    type: Schema.Types.Mixed,
  },
  statusHistory: [{
    status: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
    },
    note: {
      type: String,
      trim: true,
    },
    source: {
      type: String,
      required: true,
      enum: ['system', 'webhook', 'manual', 'retry'],
      default: 'system',
    },
  }],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Compound indexes for efficient queries
paymentSchema.index({ user: 1, status: 1 });
paymentSchema.index({ user: 1, createdAt: -1 });
paymentSchema.index({ status: 1, createdAt: -1 });
paymentSchema.index({ paymentMethod: 1, provider: 1 });
paymentSchema.index({ 'mpesaDetails.phoneNumber': 1 });
paymentSchema.index({ webhookReceived: 1, status: 1 });
paymentSchema.index({ retryCount: 1, status: 1 });

// Virtual for payment age
paymentSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60));
});

// Virtual for is expired (payments expire after 5 minutes for STK Push)
paymentSchema.virtual('isExpired').get(function() {
  if (this.status === 'pending' && this.paymentMethod === 'mpesa') {
    return this.ageInMinutes > 5;
  }
  return false;
});

// Pre-save middleware to add status history
paymentSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      source: 'system',
    });
  }
  next();
});

// Static method to generate unique payment ID
paymentSchema.statics.generatePaymentId = function(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `PAY_${timestamp}_${random}`.toUpperCase();
};

export default mongoose.model<IPayment>('Payment', paymentSchema);
