import mongoose from 'mongoose';
import { notificationIntegrationService } from '@/services/notificationIntegration';
import { notificationService } from '@/services/notification';
import User from '@/models/User';
import { logger } from '@/utils/logger';

/**
 * Test script to verify notification integration is working correctly
 */
async function testNotificationIntegration(): Promise<void> {
  try {
    logger.info('Starting notification integration tests...');

    // Find a test user (or create one)
    let testUser = await User.findOne({ phoneNumber: '+254700000000' });
    
    if (!testUser) {
      logger.info('Creating test user for notification testing...');
      testUser = new User({
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+254700000000',
        email: '<EMAIL>',
        isVerified: true,
        isActive: true,
        pediTokens: 100,
      });
      await testUser.save();
      logger.info('Test user created:', { userId: testUser._id });
    }

    const userId = testUser._id.toString();

    // Test 1: Welcome notification
    logger.info('Testing welcome notification...');
    await notificationIntegrationService.sendWelcomeNotification(userId, testUser.firstName);
    
    // Test 2: Token earned notification
    logger.info('Testing token earned notification...');
    await notificationIntegrationService.sendTokenEarned(
      userId,
      50,
      'completing your profile',
      'test-transaction-123'
    );

    // Test 3: Exchange request notification
    logger.info('Testing exchange request notification...');
    await notificationIntegrationService.sendExchangeRequest(
      userId,
      'Jane Doe',
      'Blue Denim Jacket',
      'exchange-123'
    );

    // Test 4: Exchange status update notification
    logger.info('Testing exchange status update notification...');
    await notificationIntegrationService.sendExchangeStatusUpdate(
      userId,
      'accepted',
      'Blue Denim Jacket',
      'Jane Doe',
      'exchange-123'
    );

    // Test 5: Verification reminder
    logger.info('Testing verification reminder...');
    await notificationIntegrationService.sendVerificationReminder(userId, 'phone');

    // Test 6: System alert
    logger.info('Testing system alert...');
    await notificationIntegrationService.sendSystemAlert(
      userId,
      'maintenance',
      'Scheduled maintenance will occur tonight from 2-4 AM EAT. The app may be temporarily unavailable.'
    );

    // Test 7: Reminder notification
    logger.info('Testing reminder notification...');
    await notificationIntegrationService.sendReminder(
      userId,
      'pickup',
      'Your item pickup is scheduled for tomorrow at 2 PM at Westlands Mall',
      'transaction-456'
    );

    // Wait a moment for notifications to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check notification statistics
    logger.info('Checking notification statistics...');
    const stats = await notificationService.getNotificationStats(userId);
    logger.info('Notification stats:', stats);

    // Get recent notifications for the user
    const recentNotifications = await notificationService.getUserNotifications(userId, {
      limit: 10,
      skip: 0,
    });

    logger.info('Recent notifications:', {
      total: recentNotifications.length,
      notifications: recentNotifications.map(n => ({
        id: n._id,
        category: n.category,
        title: n.title,
        status: n.status,
        createdAt: n.createdAt,
      })),
    });

    // Test notification service health
    logger.info('Testing notification service health...');
    const healthStatus = await notificationService.getServiceHealth();
    logger.info('Service health:', healthStatus);

    logger.info('✅ All notification integration tests completed successfully!');

  } catch (error) {
    logger.error('❌ Notification integration test failed:', error);
    throw error;
  }
}

/**
 * Test bulk notification functionality
 */
async function testBulkNotifications(): Promise<void> {
  try {
    logger.info('Testing bulk notification functionality...');

    // Find multiple test users
    const testUsers = await User.find({ isVerified: true }).limit(3);
    
    if (testUsers.length === 0) {
      logger.warn('No verified users found for bulk notification testing');
      return;
    }

    const userIds = testUsers.map(user => user._id.toString());

    // Test bulk promotional notification
    const result = await notificationIntegrationService.sendPromotionalNotification(
      userIds,
      'Weekend Special',
      'Get 20% extra tokens on all exchanges this weekend!',
      'Check out the latest sustainable fashion trends'
    );

    logger.info('Bulk notification result:', result);

    logger.info('✅ Bulk notification test completed successfully!');

  } catch (error) {
    logger.error('❌ Bulk notification test failed:', error);
    throw error;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/pedi';
  
  mongoose.connect(mongoUri)
    .then(async () => {
      logger.info('Connected to MongoDB for notification testing');
      
      // Run individual notification tests
      await testNotificationIntegration();
      
      // Run bulk notification tests
      await testBulkNotifications();
      
      await mongoose.disconnect();
      logger.info('Testing completed and disconnected from MongoDB');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Failed to connect to MongoDB:', error);
      process.exit(1);
    });
}

export { testNotificationIntegration, testBulkNotifications };
