import jwt from 'jsonwebtoken';
import { Socket } from 'socket.io';
import User from '@/models/User';
import { logger } from '@/utils/logger';

export interface AuthenticatedSocket extends Socket {
  userId: string;
  user: any;
}

export const socketAuthMiddleware = async (socket: any, next: any) => {
  try {
    // Extract token from handshake auth
    const token = socket.handshake.auth.token;
    
    if (!token) {
      return next(new Error('Authentication token required'));
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env['JWT_SECRET']!) as any;
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      return next(new Error('User not found'));
    }

    // Attach user to socket
    socket.userId = user._id.toString();
    socket.user = user;
    
    logger.info(`Socket authenticated for user: ${user.firstName} ${user.lastName}`);
    next();
  } catch (error: any) {
    logger.error('Socket authentication failed:', error.message);
    next(new Error('Authentication failed'));
  }
};
