import { Request, Response, NextFunction } from 'express';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    phoneNumber: string;
    fullName: string;
  };
}

/**
 * Middleware to track user activity for analytics
 * This is a placeholder implementation that can be extended with actual analytics tracking
 */
export const trackUserActivity = (action: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // Log the activity (in production, this would send to analytics service)
      const activityData = {
        userId: req.user?.id,
        action,
        timestamp: new Date(),
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method
      };

      // For now, just log to console
      // In production, you would send this to your analytics service
      console.log('User Activity:', activityData);

      // Continue to next middleware
      next();
    } catch (error) {
      console.error('Analytics tracking error:', error);
      // Don't fail the request if analytics fails
      next();
    }
  };
};

/**
 * Middleware to track API endpoint usage
 */
export const trackApiUsage = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();

    // Override res.end to capture response time
    const originalEnd = res.end;
    res.end = function(...args: any[]): Response {
      const responseTime = Date.now() - startTime;
      
      const apiUsageData = {
        userId: req.user?.id,
        endpoint: req.path,
        method: req.method,
        statusCode: res.statusCode,
        responseTime,
        timestamp: new Date(),
        ip: req.ip,
        userAgent: req.get('User-Agent')
      };

      // Log API usage (in production, send to analytics service)
      console.log('API Usage:', apiUsageData);

      // Call original end method
      return originalEnd.apply(this, args);
    };

    next();
  } catch (error) {
    console.error('API usage tracking error:', error);
    next();
  }
};

/**
 * Middleware to track errors for analytics
 */
export const trackError = (error: Error, req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const errorData = {
      userId: req.user?.id,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      endpoint: req.path,
      method: req.method,
      timestamp: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent')
    };

    // Log error (in production, send to error tracking service)
    console.error('Error tracked:', errorData);

    next(error);
  } catch (trackingError) {
    console.error('Error tracking failed:', trackingError);
    next(error);
  }
};
