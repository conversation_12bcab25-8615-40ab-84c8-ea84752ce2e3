import { Request, Response, NextFunction } from 'express';
import { query, validationResult } from 'express-validator';
import { createError } from '@/middleware/errorHandler';

// Generic validation error handler
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
    return;
  }
  next();
};

// Pagination validation middleware
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sortBy')
    .optional()
    .isString()
    .withMessage('Sort by must be a string'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }
    next();
  }
];

// Search validation middleware
export const validateSearch = [
  query('search')
    .optional()
    .isString()
    .isLength({ max: 100 })
    .withMessage('Search query must be less than 100 characters'),
  query('category')
    .optional()
    .isString()
    .withMessage('Category must be a string'),
  query('size')
    .optional()
    .isString()
    .withMessage('Size must be a string'),
  query('condition')
    .optional()
    .isString()
    .withMessage('Condition must be a string'),
  query('exchangeType')
    .optional()
    .isIn(['swap', 'token', 'donation', 'all'])
    .withMessage('Invalid exchange type'),
  query('county')
    .optional()
    .isString()
    .withMessage('County must be a string'),
  query('minPrice')
    .optional()
    .isNumeric()
    .withMessage('Min price must be a number'),
  query('maxPrice')
    .optional()
    .isNumeric()
    .withMessage('Max price must be a number'),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }
    next();
  }
];

// MongoDB ObjectId validation
export const validateObjectId = (paramName: string) => [
  query(paramName)
    .isMongoId()
    .withMessage(`${paramName} must be a valid MongoDB ObjectId`),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }
    next();
  }
];

// File upload validation
export const validateFileUpload = (req: Request, res: Response, next: NextFunction) => {
  if (!req.files || (Array.isArray(req.files) && req.files.length === 0)) {
    return next(createError('No files uploaded', 400));
  }
  
  const files = Array.isArray(req.files) ? req.files : [req.files];
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  for (const file of files) {
    if (file && typeof file === 'object' && 'size' in file && 'mimetype' in file) {
      if ((file.size as number) > maxSize) {
        return next(createError('File size too large. Maximum size is 5MB', 400));
      }

      if (!allowedTypes.includes(file.mimetype as string)) {
        return next(createError('Invalid file type. Only JPEG, PNG, and WebP are allowed', 400));
      }
    }
  }
  
  next();
};

// Rate limiting validation
export const validateRateLimit = (maxRequests: number, windowMs: number) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    
    const clientData = requests.get(clientId);
    
    if (!clientData || now > clientData.resetTime) {
      requests.set(clientId, {
        count: 1,
        resetTime: now + windowMs
      });
      return next();
    }
    
    if (clientData.count >= maxRequests) {
      return next(createError('Too many requests. Please try again later.', 429));
    }
    
    clientData.count++;
    next();
  };
};

// Phone number validation for Kenya
export const validateKenyanPhone = (fieldName: string) => [
  query(fieldName)
    .matches(/^(\+254|254|0)[17]\d{8}$/)
    .withMessage('Invalid Kenyan phone number format'),
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }
    next();
  }
];
