import mongoose from 'mongoose';
import Payment from '@/models/Payment';
import User from '@/models/User';
import TokenTransaction from '@/models/TokenTransaction';
import { logger } from '@/utils/logger';

export interface MpesaCallbackData {
  Body: {
    stkCallback: {
      MerchantRequestID: string;
      CheckoutRequestID: string;
      ResultCode: number;
      ResultDesc: string;
      CallbackMetadata?: {
        Item: Array<{
          Name: string;
          Value: string | number;
        }>;
      };
    };
  };
}

export class CallbackHandler {
  /**
   * Process M-Pesa STK Push callback
   */
  async processSTKCallback(callbackData: MpesaCallbackData): Promise<void> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { Body } = callbackData;
      
      if (!Body || !Body.stkCallback) {
        throw new Error('Invalid callback data structure');
      }

      const { 
        MerchantRequestID, 
        CheckoutRequestID, 
        ResultCode, 
        ResultDesc,
        CallbackMetadata 
      } = Body.stkCallback;

      logger.info('Processing M-Pesa STK callback', {
        merchantRequestId: MerchantRequestID,
        checkoutRequestId: CheckoutRequestID,
        resultCode: ResultCode,
        resultDesc: ResultDesc
      });

      // Find payment by merchant request ID
      const payment = await Payment.findOne({
        merchantRequestId: MerchantRequestID
      }).session(session);

      if (!payment) {
        logger.error('Payment not found for M-Pesa callback', {
          merchantRequestId: MerchantRequestID,
          checkoutRequestId: CheckoutRequestID
        });
        throw new Error('Payment not found');
      }

      // Update payment with callback data
      payment.callbackData = callbackData;
      payment.webhookReceived = true;
      payment.webhookReceivedAt = new Date();

      if (ResultCode === 0) {
        // Payment successful
        await this.handleSuccessfulPayment(payment, CallbackMetadata, session);
      } else {
        // Payment failed
        await this.handleFailedPayment(payment, ResultDesc, session);
      }

      await session.commitTransaction();

      logger.info('M-Pesa callback processed successfully', {
        paymentId: payment.paymentId,
        merchantRequestId: MerchantRequestID,
        resultCode: ResultCode,
        status: payment.status
      });

    } catch (error: any) {
      await session.abortTransaction();
      
      logger.error('Failed to process M-Pesa callback', {
        error: error.message,
        callbackData
      });
      
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Handle successful payment
   */
  private async handleSuccessfulPayment(
    payment: any, 
    callbackMetadata: any, 
    session: mongoose.ClientSession
  ): Promise<void> {
    try {
      // Extract payment details from callback metadata
      const paymentDetails = this.extractPaymentDetails(callbackMetadata);
      
      // Update payment status
      payment.status = 'completed';
      payment.mpesaReceiptNumber = paymentDetails.mpesaReceiptNumber;
      payment.statusHistory.push({
        status: 'completed',
        timestamp: new Date(),
        source: 'webhook',
        note: `Payment completed successfully. Receipt: ${paymentDetails.mpesaReceiptNumber}`
      });

      // Update M-Pesa details
      if (payment.mpesaDetails) {
        payment.mpesaDetails.resultCode = 0;
        payment.mpesaDetails.resultDescription = 'Payment completed successfully';
        payment.mpesaDetails.transactionDate = paymentDetails.transactionDate;
      }

      await payment.save({ session });

      // Award tokens to user
      await this.awardTokensToUser(payment, session);

      logger.info('Payment processed successfully', {
        paymentId: payment.paymentId,
        userId: payment.user,
        amount: payment.amount,
        mpesaReceiptNumber: paymentDetails.mpesaReceiptNumber
      });

    } catch (error: any) {
      logger.error('Failed to handle successful payment', {
        error: error.message,
        paymentId: payment.paymentId
      });
      throw error;
    }
  }

  /**
   * Handle failed payment
   */
  private async handleFailedPayment(
    payment: any, 
    resultDesc: string, 
    session: mongoose.ClientSession
  ): Promise<void> {
    try {
      payment.status = 'failed';
      payment.failureReason = resultDesc;
      payment.statusHistory.push({
        status: 'failed',
        timestamp: new Date(),
        source: 'webhook',
        note: `Payment failed: ${resultDesc}`
      });

      // Update M-Pesa details
      if (payment.mpesaDetails) {
        payment.mpesaDetails.resultDescription = resultDesc;
      }

      await payment.save({ session });

      logger.info('Payment marked as failed', {
        paymentId: payment.paymentId,
        userId: payment.user,
        reason: resultDesc
      });

    } catch (error: any) {
      logger.error('Failed to handle failed payment', {
        error: error.message,
        paymentId: payment.paymentId
      });
      throw error;
    }
  }

  /**
   * Award tokens to user after successful payment
   */
  private async awardTokensToUser(payment: any, session: mongoose.ClientSession): Promise<void> {
    try {
      const { tokens, bonus, totalTokens } = payment.metadata;
      
      // Update user's token balance
      const user = await User.findById(payment.user).session(session);
      if (!user) {
        throw new Error('User not found for token award');
      }

      const previousBalance = user.pediTokens;
      user.pediTokens += totalTokens;
      user.totalTokensPurchased += totalTokens;
      await user.save({ session });

      // Create token transaction record
      const tokenTransaction = new TokenTransaction({
        user: payment.user,
        type: 'earn',
        amount: totalTokens,
        balance: user.pediTokens,
        source: {
          type: 'purchase',
          referenceId: payment._id,
          description: `Token purchase - ${payment.description}`
        },
        metadata: {
          paymentId: payment.paymentId,
          packageTokens: tokens,
          bonusTokens: bonus,
          mpesaReceiptNumber: payment.mpesaReceiptNumber,
          previousBalance,
          purchaseAmount: payment.amount
        }
      });

      await tokenTransaction.save({ session });

      logger.info('Tokens awarded successfully', {
        userId: payment.user,
        paymentId: payment.paymentId,
        tokensAwarded: totalTokens,
        previousBalance,
        newBalance: user.pediTokens
      });

    } catch (error: any) {
      logger.error('Failed to award tokens to user', {
        error: error.message,
        paymentId: payment.paymentId,
        userId: payment.user
      });
      throw error;
    }
  }

  /**
   * Extract payment details from M-Pesa callback metadata
   */
  private extractPaymentDetails(callbackMetadata: any): any {
    const details: any = {};

    if (callbackMetadata && callbackMetadata.Item) {
      callbackMetadata.Item.forEach((item: any) => {
        switch (item.Name) {
          case 'Amount':
            details.amount = parseFloat(item.Value);
            break;
          case 'MpesaReceiptNumber':
            details.mpesaReceiptNumber = item.Value;
            break;
          case 'TransactionDate':
            details.transactionDate = new Date(item.Value.toString());
            break;
          case 'PhoneNumber':
            details.phoneNumber = item.Value;
            break;
          case 'Balance':
            details.balance = parseFloat(item.Value);
            break;
        }
      });
    }

    return details;
  }

  /**
   * Validate callback authenticity (implement webhook signature verification)
   */
  validateCallback(callbackData: any, signature?: string): boolean {
    try {
      // In production, implement proper signature verification
      // For now, basic validation
      if (!callbackData || !callbackData.Body || !callbackData.Body.stkCallback) {
        return false;
      }

      const { MerchantRequestID, CheckoutRequestID, ResultCode } = callbackData.Body.stkCallback;
      
      return !!(MerchantRequestID && CheckoutRequestID && typeof ResultCode === 'number');

    } catch (error) {
      logger.error('Callback validation failed', { error, callbackData });
      return false;
    }
  }

  /**
   * Handle callback timeout (when payment takes too long)
   */
  async handleCallbackTimeout(paymentId: string): Promise<void> {
    try {
      const payment = await Payment.findOne({ paymentId });
      
      if (!payment) {
        logger.error('Payment not found for timeout handling', { paymentId });
        return;
      }

      if (payment.status === 'processing') {
        payment.status = 'failed';
        payment.failureReason = 'Payment timeout - no callback received';
        payment.statusHistory.push({
          status: 'failed',
          timestamp: new Date(),
          source: 'system',
          note: 'Payment timed out - no callback received within expected time'
        });

        await payment.save();

        logger.info('Payment marked as timed out', {
          paymentId,
          userId: payment.user
        });
      }

    } catch (error: any) {
      logger.error('Failed to handle payment timeout', {
        error: error.message,
        paymentId
      });
    }
  }

  /**
   * Retry failed callback processing
   */
  async retryCallbackProcessing(paymentId: string): Promise<boolean> {
    try {
      const payment = await Payment.findOne({ paymentId });
      
      if (!payment || !payment.callbackData) {
        return false;
      }

      if (payment.retryCount >= payment.maxRetries) {
        logger.warn('Maximum retry attempts exceeded', { paymentId });
        return false;
      }

      payment.retryCount += 1;
      payment.lastRetryAt = new Date();
      await payment.save();

      // Reprocess the callback
      await this.processSTKCallback(payment.callbackData);
      
      return true;

    } catch (error: any) {
      logger.error('Callback retry failed', {
        error: error.message,
        paymentId
      });
      return false;
    }
  }
}

export const callbackHandler = new CallbackHandler();
