import mongoose from 'mongoose';
import Payment, { IPayment } from '@/models/Payment';
import PaymentMethod, { IPaymentMethod } from '@/models/PaymentMethod';
import { getMpesaService, MpesaPaymentRequest, MpesaCallbackData } from '@/services/mpesa';
import { logger } from '@/utils/logger';

export interface CreatePaymentRequest {
  userId: string;
  amount: number;
  description: string;
  phoneNumber?: string;
  paymentMethodId?: string;
  relatedTransaction?: string;
  relatedEntity?: {
    type: 'token_purchase' | 'platform_fee' | 'premium_feature' | 'delivery_fee';
    id: string;
  };
  metadata?: any;
}

export interface PaymentStatusUpdate {
  paymentId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  note?: string;
  source?: 'system' | 'webhook' | 'manual' | 'retry';
}

export class PaymentService {
  /**
   * Create a new payment
   */
  async createPayment(request: CreatePaymentRequest): Promise<IPayment> {
    try {
      const paymentId = (Payment as any).generatePaymentId();
      
      // Get payment method if provided
      let paymentMethod: IPaymentMethod | null = null;
      if (request.paymentMethodId) {
        paymentMethod = await PaymentMethod.findById(request.paymentMethodId);
        if (!paymentMethod || paymentMethod.user.toString() !== request.userId) {
          throw new Error('Invalid payment method');
        }
      }

      // Create payment record
      const payment = new Payment({
        paymentId,
        amount: request.amount,
        currency: 'KES',
        description: request.description,
        user: request.userId,
        relatedTransaction: request.relatedTransaction,
        relatedEntity: request.relatedEntity,
        paymentMethod: paymentMethod?.type || 'mpesa',
        provider: paymentMethod?.provider || 'safaricom',
        status: 'pending',
        retryCount: 0,
        maxRetries: 3,
        webhookReceived: false,
        metadata: request.metadata,
        statusHistory: [{
          status: 'pending',
          timestamp: new Date(),
          source: 'system',
          note: 'Payment created',
        }],
      });

      await payment.save();

      logger.info('Payment created successfully', {
        paymentId: payment.paymentId,
        userId: request.userId,
        amount: request.amount,
      });

      return payment;
    } catch (error: any) {
      logger.error('Failed to create payment:', error);
      throw new Error(`Failed to create payment: ${error.message}`);
    }
  }

  /**
   * Initiate M-Pesa STK Push payment
   */
  async initiateSTKPush(paymentId: string, phoneNumber: string): Promise<IPayment> {
    try {
      const payment = await Payment.findOne({ paymentId });
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'pending') {
        throw new Error('Payment is not in pending status');
      }

      // Prepare M-Pesa request
      const mpesaRequest: MpesaPaymentRequest = {
        amount: payment.amount,
        phoneNumber,
        accountReference: payment.paymentId,
        transactionDesc: payment.description,
      };

      // Initiate STK Push
      const mpesaResponse = await getMpesaService().initiateSTKPush(mpesaRequest);

      // Update payment with M-Pesa details
      payment.merchantRequestId = mpesaResponse.merchantRequestId;
      payment.checkoutRequestId = mpesaResponse.checkoutRequestId;
      payment.status = 'processing';
      payment.mpesaDetails = {
        phoneNumber,
        accountReference: payment.paymentId,
        transactionType: 'STK_PUSH',
      };

      payment.statusHistory.push({
        status: 'processing',
        timestamp: new Date(),
        source: 'system',
        note: 'STK Push initiated',
      });

      await payment.save();

      logger.info('STK Push initiated successfully', {
        paymentId: payment.paymentId,
        merchantRequestId: mpesaResponse.merchantRequestId,
        checkoutRequestId: mpesaResponse.checkoutRequestId,
      });

      return payment;
    } catch (error: any) {
      logger.error('Failed to initiate STK Push:', error);
      throw new Error(`Failed to initiate STK Push: ${error.message}`);
    }
  }

  /**
   * Process M-Pesa callback
   */
  async processCallback(callbackData: MpesaCallbackData): Promise<void> {
    try {
      const { stkCallback } = callbackData.Body;
      const checkoutRequestId = stkCallback.CheckoutRequestID;

      // Find payment by checkout request ID
      const payment = await Payment.findOne({ checkoutRequestId });
      if (!payment) {
        logger.warn('Payment not found for callback', { checkoutRequestId });
        return;
      }

      // Process callback using M-Pesa service
      const mpesaService = getMpesaService();
      const result = mpesaService.processSTKCallback(callbackData);

      // Update payment based on callback result
      payment.webhookReceived = true;
      payment.webhookReceivedAt = new Date();
      payment.callbackData = callbackData;

      if (result.success && result.transactionDetails) {
        // Payment successful
        payment.status = 'completed';
        payment.mpesaReceiptNumber = result.transactionDetails.mpesaReceiptNumber;
        
        if (payment.mpesaDetails) {
          payment.mpesaDetails.resultCode = 0;
          payment.mpesaDetails.resultDescription = 'Payment successful';
          payment.mpesaDetails.transactionDate = new Date(result.transactionDetails.transactionDate);
        }

        payment.statusHistory.push({
          status: 'completed',
          timestamp: new Date(),
          source: 'webhook',
          note: 'Payment completed via M-Pesa callback',
        });

        // Update payment method usage stats if applicable
        const paymentMethod = await PaymentMethod.findOne({
          user: payment.user,
          'mpesaDetails.phoneNumber': result.transactionDetails.phoneNumber,
        });
        
        if (paymentMethod) {
          await paymentMethod.updateUsageStats(payment.amount, true);
        }

        logger.info('Payment completed successfully', {
          paymentId: payment.paymentId,
          mpesaReceiptNumber: payment.mpesaReceiptNumber,
          amount: payment.amount,
        });

        // Send payment confirmation notification
        try {
          const { notificationIntegrationService } = await import('./notificationIntegration');
          await notificationIntegrationService.sendPaymentConfirmation(payment);
        } catch (notificationError) {
          logger.error('Failed to send payment confirmation notification:', notificationError);
        }

      } else {
        // Payment failed
        payment.status = 'failed';
        payment.failureReason = result.error || 'Payment failed';
        
        if (payment.mpesaDetails) {
          payment.mpesaDetails.resultCode = stkCallback.ResultCode;
          payment.mpesaDetails.resultDescription = stkCallback.ResultDesc;
        }

        payment.statusHistory.push({
          status: 'failed',
          timestamp: new Date(),
          source: 'webhook',
          note: `Payment failed: ${result.error}`,
        });

        logger.warn('Payment failed', {
          paymentId: payment.paymentId,
          resultCode: stkCallback.ResultCode,
          resultDesc: stkCallback.ResultDesc,
        });

        // Send payment failure notification
        try {
          const { notificationIntegrationService } = await import('./notificationIntegration');
          await notificationIntegrationService.sendPaymentFailure(payment);
        } catch (notificationError) {
          logger.error('Failed to send payment failure notification:', notificationError);
        }
      }

      await payment.save();

      // Trigger post-payment processing for completed payments
      if (payment.status === 'completed' && payment.relatedEntity) {
        await this.handlePaymentCompletion(payment);
      }

    } catch (error: any) {
      logger.error('Failed to process payment callback:', error);
      throw new Error(`Failed to process callback: ${error.message}`);
    }
  }

  /**
   * Handle post-payment completion processing
   */
  private async handlePaymentCompletion(payment: IPayment): Promise<void> {
    try {
      if (!payment.relatedEntity) return;

      const { type, id } = payment.relatedEntity;

      switch (type) {
        case 'platform_fee':
          // Import here to avoid circular dependency
          const { paymentIntegrationService } = await import('./paymentIntegration');
          await paymentIntegrationService.completePlatformFeePayment(payment.paymentId);
          logger.info('Platform fee payment completed', { paymentId: payment.paymentId, transactionId: id });
          break;

        case 'token_purchase':
          // Handle token top-up completion
          const { paymentIntegrationService: tokenService } = await import('./paymentIntegration');
          await tokenService.completeTokenTopUp(payment.paymentId);
          logger.info('Token top-up completed', { paymentId: payment.paymentId, userId: id });
          break;

        case 'delivery_fee':
          // Handle delivery fee completion (could trigger delivery coordination)
          logger.info('Delivery fee payment completed', { paymentId: payment.paymentId, transactionId: id });
          break;

        case 'premium_feature':
          // Handle premium feature activation
          logger.info('Premium feature payment completed', { paymentId: payment.paymentId, featureId: id });
          break;

        default:
          logger.warn('Unknown payment type for completion handling', { type, paymentId: payment.paymentId });
      }
    } catch (error: any) {
      logger.error('Failed to handle payment completion:', error);
      // Don't throw here to avoid affecting the main payment processing
    }
  }

  /**
   * Query payment status from M-Pesa
   */
  async queryPaymentStatus(paymentId: string): Promise<IPayment> {
    try {
      const payment = await Payment.findOne({ paymentId });
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (!payment.checkoutRequestId) {
        throw new Error('No checkout request ID found for payment');
      }

      // Query M-Pesa for status
      const mpesaService = getMpesaService();
      const statusResponse = await mpesaService.querySTKPushStatus(payment.checkoutRequestId);

      // Update payment based on status response
      if (statusResponse.ResultCode === '0') {
        // Payment successful
        payment.status = 'completed';
        payment.statusHistory.push({
          status: 'completed',
          timestamp: new Date(),
          source: 'system',
          note: 'Payment status confirmed via query',
        });
      } else if (statusResponse.ResultCode !== '1032') {
        // Payment failed (1032 means still pending)
        payment.status = 'failed';
        payment.failureReason = statusResponse.ResultDesc;
        payment.statusHistory.push({
          status: 'failed',
          timestamp: new Date(),
          source: 'system',
          note: `Payment failed: ${statusResponse.ResultDesc}`,
        });
      }

      await payment.save();
      return payment;

    } catch (error: any) {
      logger.error('Failed to query payment status:', error);
      throw new Error(`Failed to query payment status: ${error.message}`);
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(update: PaymentStatusUpdate): Promise<IPayment> {
    try {
      const payment = await Payment.findOne({ paymentId: update.paymentId });
      if (!payment) {
        throw new Error('Payment not found');
      }

      payment.status = update.status;
      payment.statusHistory.push({
        status: update.status,
        timestamp: new Date(),
        source: update.source || 'system',
        note: update.note,
      });

      await payment.save();

      logger.info('Payment status updated', {
        paymentId: update.paymentId,
        status: update.status,
        source: update.source,
      });

      return payment;
    } catch (error: any) {
      logger.error('Failed to update payment status:', error);
      throw new Error(`Failed to update payment status: ${error.message}`);
    }
  }

  /**
   * Get payment by ID
   */
  async getPayment(paymentId: string): Promise<IPayment | null> {
    try {
      return await Payment.findOne({ paymentId }).populate('user', 'firstName lastName phoneNumber');
    } catch (error: any) {
      logger.error('Failed to get payment:', error);
      throw new Error(`Failed to get payment: ${error.message}`);
    }
  }

  /**
   * Get user payments with pagination
   */
  async getUserPayments(
    userId: string,
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<{ payments: IPayment[]; total: number; pages: number }> {
    try {
      const query: any = { user: userId };
      if (status) {
        query.status = status;
      }

      const skip = (page - 1) * limit;
      
      const [payments, total] = await Promise.all([
        Payment.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate('user', 'firstName lastName phoneNumber'),
        Payment.countDocuments(query),
      ]);

      const pages = Math.ceil(total / limit);

      return { payments, total, pages };
    } catch (error: any) {
      logger.error('Failed to get user payments:', error);
      throw new Error(`Failed to get user payments: ${error.message}`);
    }
  }
}

export const paymentService = new PaymentService();
