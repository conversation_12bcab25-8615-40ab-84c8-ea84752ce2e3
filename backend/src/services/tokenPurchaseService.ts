import mongoose from 'mongoose';
import User from '@/models/User';
import Payment from '@/models/Payment';
import TokenTransaction from '@/models/TokenTransaction';
import { getMpesaService } from '@/services/mpesa';
import { logger } from '@/utils/logger';

// Fixed pricing tiers for demo
export const TOKEN_PACKAGES = {
  basic: { 
    id: 'basic',
    tokens: 1, 
    priceKES: 2, 
    priceUSD: 0.02, 
    bonus: 0, 
    description: 'Basic Package',
    popular: false
  },
  standard: { 
    id: 'standard',
    tokens: 2, 
    priceKES: 4, 
    priceUSD: 0.04, 
    bonus: 0, 
    description: 'Standard Package',
    popular: false
  },
  premium: { 
    id: 'premium',
    tokens: 3, 
    priceKES: 6, 
    priceUSD: 0.06, 
    bonus: 0, 
    description: 'Premium Package',
    popular: true
  },
  deluxe: { 
    id: 'deluxe',
    tokens: 4, 
    priceKES: 8, 
    priceUSD: 0.08, 
    bonus: 0, 
    description: 'Deluxe Package',
    popular: false
  }
};

export interface TokenPurchaseRequest {
  userId: string;
  packageId: keyof typeof TOKEN_PACKAGES;
  phoneNumber: string;
  paymentMethod?: 'mpesa';
}

export interface TokenPurchaseResponse {
  success: boolean;
  message: string;
  data?: {
    paymentId: string;
    merchantRequestId?: string;
    checkoutRequestId?: string;
    amount: number;
    tokens: number;
    customerMessage?: string;
  };
  error?: string;
}

export class TokenPurchaseService {
  /**
   * Get all available token packages
   */
  getTokenPackages() {
    return Object.values(TOKEN_PACKAGES);
  }

  /**
   * Get a specific token package by ID
   */
  getTokenPackage(packageId: string) {
    return TOKEN_PACKAGES[packageId as keyof typeof TOKEN_PACKAGES];
  }

  /**
   * Validate phone number format
   */
  private validatePhoneNumber(phoneNumber: string): boolean {
    // Kenyan phone number format: 254XXXXXXXXX or 07XXXXXXXX or 01XXXXXXXX
    const phoneRegex = /^(254[17]\d{8}|0[17]\d{8})$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Format phone number to M-Pesa format
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any spaces or special characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Convert to 254 format
    if (cleaned.startsWith('0')) {
      return '254' + cleaned.substring(1);
    } else if (cleaned.startsWith('254')) {
      return cleaned;
    } else {
      throw new Error('Invalid phone number format');
    }
  }

  /**
   * Initiate token purchase
   */
  async initiateTokenPurchase(request: TokenPurchaseRequest): Promise<TokenPurchaseResponse> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate inputs
      if (!request.packageId || !TOKEN_PACKAGES[request.packageId]) {
        throw new Error('Invalid package selected');
      }

      if (!request.phoneNumber || !this.validatePhoneNumber(request.phoneNumber)) {
        throw new Error('Valid Kenyan phone number is required');
      }

      // Get package details
      const packageDetails = TOKEN_PACKAGES[request.packageId];
      const formattedPhone = this.formatPhoneNumber(request.phoneNumber);

      // Verify user exists
      const user = await User.findById(request.userId).session(session);
      if (!user) {
        throw new Error('User not found');
      }

      // Generate unique payment ID
      const paymentId = `TKN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create payment record
      const payment = new Payment({
        paymentId,
        amount: packageDetails.priceKES,
        currency: 'KES',
        description: `Token Purchase - ${packageDetails.description}`,
        user: request.userId,
        relatedEntity: {
          type: 'token_purchase',
          id: new mongoose.Types.ObjectId()
        },
        paymentMethod: 'mpesa',
        provider: 'safaricom',
        status: 'pending',
        mpesaDetails: {
          phoneNumber: formattedPhone,
          accountReference: `PEDI_${request.packageId.toUpperCase()}`,
          transactionType: 'STK_PUSH'
        },
        retryCount: 0,
        maxRetries: 3,
        webhookReceived: false,
        metadata: {
          packageId: request.packageId,
          tokens: packageDetails.tokens,
          bonus: packageDetails.bonus,
          totalTokens: packageDetails.tokens + packageDetails.bonus
        },
        statusHistory: [{
          status: 'pending',
          timestamp: new Date(),
          source: 'system',
          note: 'Token purchase payment initiated'
        }]
      });

      await payment.save({ session });

      // Initiate M-Pesa STK Push
      const mpesaService = getMpesaService();
      const mpesaResponse = await mpesaService.initiateSTKPush({
        amount: packageDetails.priceKES,
        phoneNumber: formattedPhone,
        accountReference: `PEDI_${request.packageId.toUpperCase()}`,
        transactionDesc: `Pedi Tokens - ${packageDetails.description}`
      });

      // Update payment with M-Pesa details
      payment.merchantRequestId = mpesaResponse.merchantRequestId;
      payment.checkoutRequestId = mpesaResponse.checkoutRequestId;
      payment.status = 'processing';
      payment.statusHistory.push({
        status: 'processing',
        timestamp: new Date(),
        source: 'system',
        note: 'M-Pesa STK Push initiated'
      });

      await payment.save({ session });
      await session.commitTransaction();

      logger.info('Token purchase initiated successfully', {
        paymentId: payment.paymentId,
        userId: request.userId,
        packageId: request.packageId,
        amount: packageDetails.priceKES,
        tokens: packageDetails.tokens,
        merchantRequestId: mpesaResponse.merchantRequestId
      });

      return {
        success: true,
        message: 'Payment initiated successfully. Please check your phone for M-Pesa prompt.',
        data: {
          paymentId: payment.paymentId,
          merchantRequestId: mpesaResponse.merchantRequestId,
          checkoutRequestId: mpesaResponse.checkoutRequestId,
          amount: packageDetails.priceKES,
          tokens: packageDetails.tokens + packageDetails.bonus,
          customerMessage: mpesaResponse.customerMessage
        }
      };

    } catch (error: any) {
      await session.abortTransaction();
      
      logger.error('Token purchase initiation failed', {
        error: error.message,
        userId: request.userId,
        packageId: request.packageId,
        phoneNumber: request.phoneNumber
      });

      return {
        success: false,
        message: error.message || 'Failed to initiate token purchase',
        error: error.message
      };
    } finally {
      session.endSession();
    }
  }

  /**
   * Process M-Pesa callback and award tokens
   */
  async processPaymentCallback(callbackData: any): Promise<void> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { Body } = callbackData;
      
      if (!Body || !Body.stkCallback) {
        throw new Error('Invalid callback data structure');
      }

      const { 
        MerchantRequestID, 
        CheckoutRequestID, 
        ResultCode, 
        ResultDesc,
        CallbackMetadata 
      } = Body.stkCallback;

      // Find payment by merchant request ID
      const payment = await Payment.findOne({
        merchantRequestId: MerchantRequestID
      }).session(session);

      if (!payment) {
        logger.error('Payment not found for M-Pesa callback', {
          merchantRequestId: MerchantRequestID,
          checkoutRequestId: CheckoutRequestID
        });
        return;
      }

      // Update payment with callback data
      payment.callbackData = callbackData;
      payment.webhookReceived = true;
      payment.webhookReceivedAt = new Date();

      if (ResultCode === 0) {
        // Payment successful
        payment.status = 'completed';
        payment.statusHistory.push({
          status: 'completed',
          timestamp: new Date(),
          source: 'webhook',
          note: `Payment completed: ${ResultDesc}`
        });

        // Extract M-Pesa receipt number if available
        if (CallbackMetadata && CallbackMetadata.Item) {
          const receiptItem = CallbackMetadata.Item.find((item: any) => 
            item.Name === 'MpesaReceiptNumber'
          );
          if (receiptItem) {
            payment.mpesaReceiptNumber = receiptItem.Value;
          }
        }

        await payment.save({ session });

        // Award tokens to user
        await this.awardTokensToUser(payment, session);

      } else {
        // Payment failed
        payment.status = 'failed';
        payment.failureReason = ResultDesc;
        payment.statusHistory.push({
          status: 'failed',
          timestamp: new Date(),
          source: 'webhook',
          note: `Payment failed: ${ResultDesc}`
        });

        await payment.save({ session });
      }

      await session.commitTransaction();

      logger.info('M-Pesa callback processed successfully', {
        paymentId: payment.paymentId,
        merchantRequestId: MerchantRequestID,
        resultCode: ResultCode,
        status: payment.status
      });

    } catch (error: any) {
      await session.abortTransaction();
      
      logger.error('Failed to process M-Pesa callback', {
        error: error.message,
        callbackData
      });
      
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Award tokens to user after successful payment
   */
  private async awardTokensToUser(payment: any, session: mongoose.ClientSession): Promise<void> {
    try {
      const { tokens, bonus, totalTokens } = payment.metadata;
      
      // Update user's token balance
      const user = await User.findById(payment.user).session(session);
      if (!user) {
        throw new Error('User not found for token award');
      }

      const previousBalance = user.pediTokens;
      user.pediTokens += totalTokens;
      user.totalTokensPurchased += totalTokens;
      await user.save({ session });

      // Create token transaction record
      const tokenTransaction = new TokenTransaction({
        user: payment.user,
        type: 'earn',
        amount: totalTokens,
        balance: user.pediTokens,
        source: {
          type: 'purchase',
          referenceId: payment._id,
          description: `Token purchase - ${payment.description}`
        },
        metadata: {
          paymentId: payment.paymentId,
          packageTokens: tokens,
          bonusTokens: bonus,
          mpesaReceiptNumber: payment.mpesaReceiptNumber,
          previousBalance
        }
      });

      await tokenTransaction.save({ session });

      logger.info('Tokens awarded successfully', {
        userId: payment.user,
        paymentId: payment.paymentId,
        tokensAwarded: totalTokens,
        previousBalance,
        newBalance: user.pediTokens
      });

    } catch (error: any) {
      logger.error('Failed to award tokens to user', {
        error: error.message,
        paymentId: payment.paymentId,
        userId: payment.user
      });
      throw error;
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(paymentId: string): Promise<any> {
    try {
      const payment = await Payment.findOne({ paymentId })
        .populate('user', 'firstName lastName phoneNumber')
        .lean();

      if (!payment) {
        throw new Error('Payment not found');
      }

      return {
        success: true,
        data: {
          paymentId: payment.paymentId,
          status: payment.status,
          amount: payment.amount,
          currency: payment.currency,
          description: payment.description,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt,
          metadata: payment.metadata,
          mpesaDetails: payment.mpesaDetails,
          statusHistory: payment.statusHistory
        }
      };

    } catch (error: any) {
      logger.error('Failed to get payment status', {
        error: error.message,
        paymentId
      });

      return {
        success: false,
        message: error.message || 'Failed to get payment status'
      };
    }
  }
}

export const tokenPurchaseService = new TokenPurchaseService();
