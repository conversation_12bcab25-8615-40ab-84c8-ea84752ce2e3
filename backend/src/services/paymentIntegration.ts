import mongoose from 'mongoose';
import Payment from '../models/Payment';
import Transaction from '../models/Transaction';
import User from '../models/User';
import { paymentService } from './payment';
import { tokenService } from './token';

export interface TokenPurchasePaymentData {
  transactionId: string;
  paymentMethodId?: string;
  phoneNumber?: string;
}

export interface PlatformFeePaymentData {
  transactionId: string;
  feeAmount: number;
  paymentMethodId?: string;
  phoneNumber?: string;
}

export interface TokenTopUpData {
  userId: string;
  tokenAmount: number;
  cashAmount: number;
  paymentMethodId?: string;
  phoneNumber?: string;
}

class PaymentIntegrationService {
  /**
   * Process payment for token purchase platform fee
   */
  async processTokenPurchaseFee(data: PlatformFeePaymentData): Promise<{ payment: any; transaction: any }> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate transaction exists and requires payment
      const transaction = await Transaction.findById(data.transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'token_purchase') {
        throw new Error('Invalid transaction type for platform fee payment');
      }

      if (transaction.status !== 'accepted') {
        throw new Error('Transaction must be accepted before processing platform fee');
      }

      if (transaction.platformFee?.status === 'completed') {
        throw new Error('Platform fee already paid');
      }

      // Calculate platform fee if not set
      let feeAmount = data.feeAmount;
      if (!transaction.platformFee) {
        // Calculate 5% platform fee on token amount (convert to KES)
        const tokenToKesRate = 10; // 1 token = 10 KES (configurable)
        feeAmount = Math.round(transaction.tokenAmount * tokenToKesRate * 0.05);
      }

      // Create payment for platform fee
      const payment = await paymentService.createPayment({
        userId: transaction.initiator.toString(),
        amount: feeAmount,
        description: `Platform fee for token purchase - Transaction ${transaction._id}`,
        paymentMethodId: data.paymentMethodId,
        relatedEntity: {
          type: 'platform_fee',
          id: transaction._id.toString(),
        },
      });

      // Update transaction with platform fee details
      transaction.platformFee = {
        amount: feeAmount,
        currency: 'KES',
        status: 'pending',
      };

      await transaction.save({ session });

      await session.commitTransaction();

      return { payment, transaction };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Complete platform fee payment and finalize token purchase
   */
  async completePlatformFeePayment(paymentId: string): Promise<void> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get payment details
      const payment = await Payment.findOne({ paymentId }).session(session);
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'completed') {
        throw new Error('Payment not completed');
      }

      if (!payment.relatedEntity || payment.relatedEntity.type !== 'platform_fee') {
        throw new Error('Invalid payment type');
      }

      // Get related transaction
      const transaction = await Transaction.findById(payment.relatedEntity.id).session(session);
      if (!transaction) {
        throw new Error('Related transaction not found');
      }

      // Update platform fee status
      transaction.platformFee!.status = 'completed';
      transaction.platformFee!.mpesaTransactionId = payment.mpesaReceiptNumber;

      // Complete the token purchase transaction
      transaction.status = 'completed';
      transaction.timeline.push({
        status: 'completed',
        timestamp: new Date(),
        updatedBy: transaction.initiator,
        note: 'Platform fee paid and transaction completed',
      });

      await transaction.save({ session });

      // Transfer tokens from buyer to seller
      await tokenService.transferTokens({
        senderId: transaction.initiator.toString(),
        recipientId: transaction.recipient.toString(),
        amount: transaction.tokenAmount,
        message: `Token purchase - Transaction ${transaction._id}`,
      });

      // Update item status to sold
      const ClothingItem = mongoose.model('ClothingItem');
      await ClothingItem.findByIdAndUpdate(
        transaction.recipientItems[0],
        { status: 'sold' },
        { session }
      );

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Process token top-up payment
   */
  async processTokenTopUp(data: TokenTopUpData): Promise<{ payment: any; user: any }> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate user exists
      const user = await User.findById(data.userId).session(session);
      if (!user) {
        throw new Error('User not found');
      }

      // Create payment for token top-up
      const payment = await paymentService.createPayment({
        userId: data.userId,
        amount: data.cashAmount,
        description: `Token top-up - ${data.tokenAmount} tokens`,
        paymentMethodId: data.paymentMethodId,
        relatedEntity: {
          type: 'token_purchase',
          id: data.userId,
        },
      });

      await session.commitTransaction();

      return { payment, user };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Complete token top-up payment
   */
  async completeTokenTopUp(paymentId: string): Promise<void> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get payment details
      const payment = await Payment.findOne({ paymentId }).session(session);
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'completed') {
        throw new Error('Payment not completed');
      }

      if (!payment.relatedEntity || payment.relatedEntity.type !== 'token_purchase') {
        throw new Error('Invalid payment type');
      }

      // Calculate tokens to award (1 KES = 0.1 tokens)
      const tokensToAward = Math.floor(payment.amount * 0.1);

      // Award tokens to user
      await tokenService.awardTokens(
        payment.user.toString(),
        tokensToAward,
        'token_topup',
        `Token top-up payment - ${payment.paymentId}`,
        session
      );

      await session.commitTransaction();
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Process delivery fee payment
   */
  async processDeliveryFee(transactionId: string, deliveryFee: number, paymentMethodId?: string, phoneNumber?: string): Promise<any> {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate transaction exists and requires delivery
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.deliveryMethod !== 'delivery') {
        throw new Error('Transaction does not require delivery');
      }

      if (transaction.status !== 'completed') {
        throw new Error('Transaction must be completed before processing delivery fee');
      }

      // Create payment for delivery fee
      const payment = await paymentService.createPayment({
        userId: transaction.initiator.toString(),
        amount: deliveryFee,
        description: `Delivery fee - Transaction ${transaction._id}`,
        paymentMethodId,
        relatedEntity: {
          type: 'delivery_fee',
          id: transaction._id.toString(),
        },
      });

      await session.commitTransaction();

      return payment;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Get payment summary for a transaction
   */
  async getTransactionPaymentSummary(transactionId: string): Promise<{
    platformFee?: any;
    deliveryFee?: any;
    totalPaid: number;
    pendingPayments: any[];
  }> {
    const transaction = await Transaction.findById(transactionId);
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    // Get all payments related to this transaction
    const payments = await Payment.find({
      'relatedEntity.id': transactionId,
    });

    const platformFeePayment = payments.find(p => p.relatedEntity?.type === 'platform_fee');
    const deliveryFeePayment = payments.find(p => p.relatedEntity?.type === 'delivery_fee');
    const pendingPayments = payments.filter(p => p.status === 'pending' || p.status === 'processing');

    const totalPaid = payments
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0);

    return {
      platformFee: platformFeePayment ? {
        amount: platformFeePayment.amount,
        status: platformFeePayment.status,
        paymentId: platformFeePayment.paymentId,
      } : undefined,
      deliveryFee: deliveryFeePayment ? {
        amount: deliveryFeePayment.amount,
        status: deliveryFeePayment.status,
        paymentId: deliveryFeePayment.paymentId,
      } : undefined,
      totalPaid,
      pendingPayments: pendingPayments.map(p => ({
        paymentId: p.paymentId,
        amount: p.amount,
        description: p.description,
        status: p.status,
        type: p.relatedEntity?.type,
      })),
    };
  }

  /**
   * Calculate platform fee for a transaction
   */
  calculatePlatformFee(tokenAmount: number, tokenToKesRate: number = 10): number {
    // 5% platform fee on the KES equivalent of token amount
    return Math.round(tokenAmount * tokenToKesRate * 0.05);
  }

  /**
   * Get token to KES conversion rate
   */
  getTokenToKesRate(): number {
    // This could be configurable or dynamic in the future
    return 10; // 1 token = 10 KES
  }

  /**
   * Get KES to token conversion rate
   */
  getKesToTokenRate(): number {
    // This could be configurable or dynamic in the future
    return 0.1; // 1 KES = 0.1 tokens
  }
}

export const paymentIntegrationService = new PaymentIntegrationService();
