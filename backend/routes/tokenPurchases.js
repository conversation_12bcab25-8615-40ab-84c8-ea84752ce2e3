const express = require('express');
const router = express.Router();
const TokenPurchase = require('../models/TokenPurchase');
const User = require('../src/models/User').default;
const { authenticate: auth } = require('../src/middleware/auth');
const { body, validationResult } = require('express-validator');

// Token packages configuration - Fixed pricing tiers for demo
const TOKEN_PACKAGES = {
  basic: { tokens: 1, priceKES: 2, priceUSD: 0.02, bonus: 0, description: 'Basic Package' },
  standard: { tokens: 2, priceKES: 4, priceUSD: 0.04, bonus: 0, description: 'Standard Package' },
  premium: { tokens: 3, priceKES: 6, priceUSD: 0.06, bonus: 0, description: 'Premium Package' },
  deluxe: { tokens: 4, priceKES: 8, priceUSD: 0.08, bonus: 0, description: 'Deluxe Package' }
};

// Create token purchase
router.post('/', [
  auth,
  body('packageId').custom(value => {
    if (value !== 'custom' && !TOKEN_PACKAGES[value]) {
      throw new Error('Invalid package ID');
    }
    return true;
  }),
  body('paymentMethod').isIn(['mpesa', 'card']).withMessage('Invalid payment method'),
  body('phoneNumber').optional().isMobilePhone().withMessage('Valid phone number required for M-Pesa')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { packageId, paymentMethod, phoneNumber, customAmount } = req.body;

    let packageDetails;
    if (packageId === 'custom') {
      if (!customAmount || customAmount < 25) {
        return res.status(400).json({
          success: false,
          message: 'Custom amount must be at least 25 KES'
        });
      }
      
      const tokens = Math.floor(customAmount / 5); // 5 KES per token
      packageDetails = {
        tokens,
        priceKES: customAmount,
        priceUSD: customAmount / 100,
        bonus: 0
      };
    } else {
      packageDetails = TOKEN_PACKAGES[packageId];
    }

    // Validate M-Pesa phone number if required
    if (paymentMethod === 'mpesa' && !phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required for M-Pesa payments'
      });
    }

    // Create token purchase record
    const tokenPurchase = new TokenPurchase({
      user: req.user.id,
      packageId: packageId === 'custom' ? 'custom' : packageId,
      tokens: packageDetails.tokens,
      bonusTokens: packageDetails.bonus,
      totalTokens: packageDetails.tokens + packageDetails.bonus,
      amountKES: packageDetails.priceKES,
      amountUSD: packageDetails.priceUSD,
      paymentMethod,
      phoneNumber: paymentMethod === 'mpesa' ? phoneNumber : undefined,
      status: 'pending'
    });

    await tokenPurchase.save();

    // Process payment based on method
    let paymentResult;
    if (paymentMethod === 'mpesa') {
      paymentResult = await processMpesaPayment(tokenPurchase);
    } else {
      paymentResult = await processCardPayment(tokenPurchase);
    }

    if (paymentResult.success) {
      tokenPurchase.paymentReference = paymentResult.reference;
      tokenPurchase.status = 'processing';
      await tokenPurchase.save();

      res.status(201).json({
        success: true,
        message: paymentMethod === 'mpesa' 
          ? 'M-Pesa prompt sent to your phone' 
          : 'Payment processing initiated',
        data: {
          purchaseId: tokenPurchase._id,
          reference: paymentResult.reference,
          tokens: tokenPurchase.totalTokens
        }
      });
    } else {
      tokenPurchase.status = 'failed';
      tokenPurchase.failureReason = paymentResult.error;
      await tokenPurchase.save();

      res.status(400).json({
        success: false,
        message: paymentResult.error || 'Payment failed'
      });
    }

  } catch (error) {
    console.error('Error creating token purchase:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get user's token purchase history
router.get('/history', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const purchases = await TokenPurchase.find({ user: req.user.id })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TokenPurchase.countDocuments({ user: req.user.id });

    // Transform for frontend
    const transformedPurchases = purchases.map(purchase => ({
      _id: purchase._id,
      type: 'purchase',
      amount: purchase.totalTokens,
      description: `Token purchase - ${purchase.packageId} package`,
      createdAt: purchase.createdAt,
      status: purchase.status
    }));

    res.json({
      success: true,
      data: transformedPurchases,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching purchase history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get purchase details
router.get('/:id', auth, async (req, res) => {
  try {
    const purchase = await TokenPurchase.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!purchase) {
      return res.status(404).json({
        success: false,
        message: 'Purchase not found'
      });
    }

    res.json({
      success: true,
      data: purchase
    });

  } catch (error) {
    console.error('Error fetching purchase:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// M-Pesa callback endpoint
router.post('/mpesa/callback', async (req, res) => {
  try {
    const { Body } = req.body;
    
    if (!Body || !Body.stkCallback) {
      return res.status(400).json({ success: false, message: 'Invalid callback data' });
    }

    const { MerchantRequestID, CheckoutRequestID, ResultCode, ResultDesc } = Body.stkCallback;

    // Find the purchase by merchant request ID
    const purchase = await TokenPurchase.findOne({
      paymentReference: MerchantRequestID
    });

    if (!purchase) {
      console.error('Purchase not found for callback:', MerchantRequestID);
      return res.status(404).json({ success: false, message: 'Purchase not found' });
    }

    if (ResultCode === 0) {
      // Payment successful
      purchase.status = 'completed';
      purchase.completedAt = new Date();
      
      // Extract payment details from callback
      if (Body.stkCallback.CallbackMetadata && Body.stkCallback.CallbackMetadata.Item) {
        const metadata = Body.stkCallback.CallbackMetadata.Item;
        const amountItem = metadata.find(item => item.Name === 'Amount');
        const receiptItem = metadata.find(item => item.Name === 'MpesaReceiptNumber');
        const phoneItem = metadata.find(item => item.Name === 'PhoneNumber');

        if (amountItem) purchase.actualAmount = amountItem.Value;
        if (receiptItem) purchase.mpesaReceiptNumber = receiptItem.Value;
        if (phoneItem) purchase.phoneNumber = phoneItem.Value;
      }

      await purchase.save();

      // Award tokens to user
      await User.findByIdAndUpdate(purchase.user, {
        $inc: { 
          pediTokens: purchase.totalTokens,
          totalTokensPurchased: purchase.totalTokens
        }
      });

      console.log(`Tokens awarded: ${purchase.totalTokens} to user ${purchase.user}`);

    } else {
      // Payment failed
      purchase.status = 'failed';
      purchase.failureReason = ResultDesc;
      await purchase.save();

      console.log(`Payment failed for purchase ${purchase._id}: ${ResultDesc}`);
    }

    res.json({ success: true });

  } catch (error) {
    console.error('Error processing M-Pesa callback:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Admin routes
router.get('/admin/all', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let filter = {};
    if (status) filter.status = status;

    const purchases = await TokenPurchase.find(filter)
      .populate('user', 'firstName lastName phoneNumber email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TokenPurchase.countDocuments(filter);

    res.json({
      success: true,
      data: purchases,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching admin purchases:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Helper functions for payment processing
async function processMpesaPayment(purchase) {
  try {
    // This is a mock implementation
    // In production, integrate with Safaricom Daraja API
    
    const mockReference = `MPX${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
    
    // Simulate M-Pesa STK push
    console.log(`Initiating M-Pesa payment for ${purchase.amountKES} KES to ${purchase.phoneNumber}`);
    
    return {
      success: true,
      reference: mockReference
    };
    
  } catch (error) {
    console.error('M-Pesa payment error:', error);
    return {
      success: false,
      error: 'M-Pesa payment failed'
    };
  }
}

async function processCardPayment(purchase) {
  try {
    // This is a mock implementation
    // In production, integrate with payment gateway like Stripe or Flutterwave
    
    const mockReference = `CRD${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
    
    console.log(`Initiating card payment for ${purchase.amountUSD} USD`);
    
    return {
      success: true,
      reference: mockReference
    };
    
  } catch (error) {
    console.error('Card payment error:', error);
    return {
      success: false,
      error: 'Card payment failed'
    };
  }
}

module.exports = router;
