'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  ArrowRightLeft, 
  Coins, 
  Heart, 
  Search, 

  Plus,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface ExchangeItem {
  _id: string;
  title: string;
  category: string;
  size: string;
  condition: string;
  images: string[];
  tokenPrice?: number;
}
import Link from 'next/link';

interface ExchangeOffer {
  _id: string;
  type: 'swap' | 'token_purchase' | 'donation';
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'withdrawn';
  offerer: {
    _id: string;
    name: string;
    profilePicture?: string;
    location: {
      county: string;
      town: string;
    };
  };
  targetUser?: {
    _id: string;
    name: string;
    profilePicture?: string;
  };
  requestedItems: ExchangeItem[];
  offeredItems?: ExchangeItem[];
  tokenAmount?: number;
  message?: string;
  createdAt: string;
  expiresAt?: string;
}

export default function ExchangesPage() {
  const [activeTab, setActiveTab] = useState('received');
  const [offers, setOffers] = useState<ExchangeOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  useEffect(() => {
    fetchOffers();
  }, [activeTab, statusFilter, typeFilter]);

  const fetchOffers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (activeTab !== 'all') params.append('type', activeTab);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`/api/exchanges/offers?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setOffers(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch offers:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'declined':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'expired':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'swap':
        return <ArrowRightLeft className="h-4 w-4 text-blue-500" />;
      case 'token_purchase':
        return <Coins className="h-4 w-4 text-yellow-500" />;
      case 'donation':
        return <Heart className="h-4 w-4 text-red-500" />;
      default:
        return <ArrowRightLeft className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'swap':
        return 'Swap';
      case 'token_purchase':
        return 'Token Purchase';
      case 'donation':
        return 'Donation';
      default:
        return type;
    }
  };

  const filteredOffers = offers.filter(offer => {
    const matchesSearch = offer.offerer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         offer.message?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || offer.type === typeFilter;
    return matchesSearch && matchesType;
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Exchanges</h1>
          <p className="text-gray-600 mt-2">Manage your clothing exchanges, swaps, and donations</p>
        </div>
        <Link href="/exchanges/create">
          <Button className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            Create Exchange
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search offers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="declined">Declined</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="swap">Swaps</SelectItem>
                <SelectItem value="token_purchase">Token Purchases</SelectItem>
                <SelectItem value="donation">Donations</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="received">Received Offers</TabsTrigger>
          <TabsTrigger value="sent">Sent Offers</TabsTrigger>
        </TabsList>

        <TabsContent value="received" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading offers...</p>
            </div>
          ) : filteredOffers.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <ArrowRightLeft className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No offers found</h3>
                <p className="text-gray-600">You haven&apos;t received any exchange offers yet.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredOffers.map((offer) => (
                <Card key={offer._id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          {offer.offerer.profilePicture ? (
                            <img
                              src={offer.offerer.profilePicture}
                              alt={offer.offerer.name}
                              className="h-12 w-12 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-600 font-medium">
                                {offer.offerer.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            {getTypeIcon(offer.type)}
                            <Badge variant="outline">{getTypeLabel(offer.type)}</Badge>
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(offer.status)}
                              <span className="text-sm text-gray-600 capitalize">{offer.status}</span>
                            </div>
                          </div>
                          <h3 className="font-medium text-gray-900 mb-1">
                            {offer.offerer.name}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">
                            {offer.offerer.location.town}, {offer.offerer.location.county}
                          </p>
                          {offer.message && (
                            <p className="text-sm text-gray-700 mb-3">&quot;{offer.message}&quot;</p>
                          )}
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>{offer.requestedItems.length} item(s) requested</span>
                            {offer.offeredItems && (
                              <span>{offer.offeredItems.length} item(s) offered</span>
                            )}
                            {offer.tokenAmount && (
                              <span className="flex items-center">
                                <Coins className="h-3 w-3 mr-1" />
                                {offer.tokenAmount} tokens
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <Link href={`/exchanges/${offer._id}`}>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="sent" className="space-y-4">
          {/* Similar structure for sent offers */}
          <div className="text-center py-8">
            <p className="text-gray-600">Sent offers will be displayed here</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
