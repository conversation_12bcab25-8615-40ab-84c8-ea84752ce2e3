'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { 
  ArrowRightLeft, 
  Coins, 
  Heart, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  MapPin,
  User,

  Star
} from 'lucide-react';

interface ExchangeItem {
  _id: string;
  title: string;
  category: string;
  size: string;
  condition: string;
  images: string[];
  tokenPrice?: number;
}

interface ExchangeOffer {
  _id: string;
  type: 'swap' | 'token_purchase' | 'donation';
  status: 'pending' | 'accepted' | 'declined' | 'expired' | 'withdrawn';
  offerer: {
    _id: string;
    name: string;
    profilePicture?: string;
    location: {
      county: string;
      town: string;
    };
    rating: {
      average: number;
      count: number;
    };
  };
  targetUser?: {
    _id: string;
    name: string;
    profilePicture?: string;
  };
  requestedItems: ExchangeItem[];
  offeredItems?: ExchangeItem[];
  tokenAmount?: number;
  message?: string;
  deliveryMethod: string;
  createdAt: string;
  expiresAt?: string;
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
  }>;
}

export default function ExchangeDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [offer, setOffer] = useState<ExchangeOffer | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [counterOffer, setCounterOffer] = useState('');
  const [responseMessage, setResponseMessage] = useState('');


  useEffect(() => {
    if (params?.id) {
      fetchOfferDetails();
    }
  }, [params?.id]);

  const fetchOfferDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/exchanges/offers/${params?.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setOffer(data.data);
      } else {
        router.push('/exchanges');
      }
    } catch (error) {
      console.error('Failed to fetch offer details:', error);
      router.push('/exchanges');
    } finally {
      setLoading(false);
    }
  };

  const handleAccept = async () => {
    if (!offer) return;

    setActionLoading(true);
    try {
      const response = await fetch(`/api/exchanges/offers/${offer._id}/accept`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message: responseMessage.trim() || undefined
        })
      });

      if (response.ok) {
        await fetchOfferDetails();
        setResponseMessage('');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to accept offer');
      }
    } catch (error) {
      console.error('Failed to accept offer:', error);
      alert('Failed to accept offer');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDecline = async () => {
    if (!offer) return;

    setActionLoading(true);
    try {
      const response = await fetch(`/api/exchanges/offers/${offer._id}/decline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          reason: responseMessage.trim() || 'Offer declined'
        })
      });

      if (response.ok) {
        await fetchOfferDetails();
        setResponseMessage('');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to decline offer');
      }
    } catch (error) {
      console.error('Failed to decline offer:', error);
      alert('Failed to decline offer');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCounterOffer = async () => {
    if (!offer || !counterOffer) return;

    setActionLoading(true);
    try {
      const response = await fetch(`/api/exchanges/offers/${offer._id}/counter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          counterOffer: offer.type === 'token_purchase' ? parseInt(counterOffer) : counterOffer,
          message: responseMessage.trim() || undefined
        })
      });

      if (response.ok) {
        await fetchOfferDetails();
        setCounterOffer('');
        setResponseMessage('');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to create counter offer');
      }
    } catch (error) {
      console.error('Failed to create counter offer:', error);
      alert('Failed to create counter offer');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'accepted':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'declined':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'expired':
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'swap':
        return <ArrowRightLeft className="h-5 w-5 text-blue-500" />;
      case 'token_purchase':
        return <Coins className="h-5 w-5 text-yellow-500" />;
      case 'donation':
        return <Heart className="h-5 w-5 text-red-500" />;
      default:
        return <ArrowRightLeft className="h-5 w-5" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'swap':
        return 'Swap';
      case 'token_purchase':
        return 'Token Purchase';
      case 'donation':
        return 'Donation';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading exchange details...</p>
        </div>
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold text-gray-900">Exchange not found</h2>
          <p className="text-gray-600 mt-2">The exchange you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/exchanges')} className="mt-4">
            Back to Exchanges
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="mb-4"
        >
          ← Back
        </Button>
        <div className="flex items-center space-x-3">
          {getTypeIcon(offer.type)}
          <h1 className="text-3xl font-bold text-gray-900">{getTypeLabel(offer.type)} Details</h1>
          <div className="flex items-center space-x-2">
            {getStatusIcon(offer.status)}
            <Badge variant="outline" className="capitalize">{offer.status}</Badge>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Offer Details */}
          <Card>
            <CardHeader>
              <CardTitle>Exchange Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {offer.offerer.profilePicture ? (
                    <img
                      src={offer.offerer.profilePicture}
                      alt={offer.offerer.name}
                      className="h-16 w-16 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="h-8 w-8 text-gray-600" />
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{offer.offerer.name}</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{offer.offerer.location.town}, {offer.offerer.location.county}</span>
                  </div>
                  <div className="flex items-center space-x-1 mt-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600">{offer.offerer.rating.average.toFixed(1)}</span>
                  </div>
                </div>
              </div>

              {offer.message && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <MessageSquare className="h-4 w-4 text-gray-500 mt-0.5" />
                    <p className="text-gray-700">{offer.message}</p>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-gray-600">Delivery Method</Label>
                  <p className="font-medium capitalize">{offer.deliveryMethod}</p>
                </div>
                <div>
                  <Label className="text-gray-600">Created</Label>
                  <p className="font-medium">{new Date(offer.createdAt).toLocaleDateString()}</p>
                </div>
              </div>

              {offer.tokenAmount && (
                <div className="flex items-center space-x-2">
                  <Coins className="h-4 w-4 text-yellow-500" />
                  <span className="font-medium">{offer.tokenAmount} tokens</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle>Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {offer.requestedItems.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Requested Items</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {offer.requestedItems.map((item: ExchangeItem) => (
                        <div key={item._id} className="border rounded-lg p-3">
                          <img
                            src={item.images[0] || '/placeholder-image.jpg'}
                            alt={item.title}
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <h5 className="font-medium text-sm">{item.title}</h5>
                          <p className="text-xs text-gray-600">{item.category} • {item.condition}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {offer.offeredItems && offer.offeredItems.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Offered Items</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {offer.offeredItems.map((item: ExchangeItem) => (
                        <div key={item._id} className="border rounded-lg p-3">
                          <img
                            src={item.images[0] || '/placeholder-image.jpg'}
                            alt={item.title}
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <h5 className="font-medium text-sm">{item.title}</h5>
                          <p className="text-xs text-gray-600">{item.category} • {item.condition}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          {offer.status === 'pending' && (
            <Card>
              <CardHeader>
                <CardTitle>Respond to Offer</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="responseMessage">Message (Optional)</Label>
                  <Textarea
                    id="responseMessage"
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    placeholder="Add a response message..."
                    rows={3}
                  />
                </div>

                {offer.type === 'token_purchase' && (
                  <div>
                    <Label htmlFor="counterOffer">Counter Offer (Tokens)</Label>
                    <Input
                      id="counterOffer"
                      type="number"
                      value={counterOffer}
                      onChange={(e) => setCounterOffer(e.target.value)}
                      placeholder="Enter counter offer amount"
                    />
                  </div>
                )}

                <div className="flex space-x-3">
                  <Button
                    onClick={handleAccept}
                    disabled={actionLoading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Accept
                  </Button>
                  <Button
                    onClick={handleDecline}
                    disabled={actionLoading}
                    variant="destructive"
                  >
                    Decline
                  </Button>
                  {offer.type === 'token_purchase' && counterOffer && (
                    <Button
                      onClick={handleCounterOffer}
                      disabled={actionLoading}
                      variant="outline"
                    >
                      Counter Offer
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {offer.timeline.map((event, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getStatusIcon(event.status)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium capitalize">{event.status.replace('_', ' ')}</p>
                      <p className="text-xs text-gray-600">
                        {new Date(event.timestamp).toLocaleString()}
                      </p>
                      {event.note && (
                        <p className="text-xs text-gray-700 mt-1">{event.note}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
