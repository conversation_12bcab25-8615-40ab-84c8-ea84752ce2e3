'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  Sparkles,
  Shirt,
  BarChart3,
  Lightbulb,
  Wand2,
  Heart,
  MessageCircle,
  Recycle,
  Star,
  Users
} from 'lucide-react';
import { 
  FashionAssistant, 
  OutfitSuggestions, 
  WardrobeAnalysis, 
  StylingTips 
} from '@/components/ai';

export default function AIAssistantPage() {
  const [activeTab, setActiveTab] = useState('chat');

  const features = [
    {
      icon: MessageCircle,
      title: 'AI Chat Assistant',
      description: 'Chat with Pedi AI for personalized fashion advice',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      icon: Wand2,
      title: 'Outfit Suggestions',
      description: 'Get AI-powered outfit recommendations',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      icon: BarChart3,
      title: 'Wardrobe Analysis',
      description: 'Analyze your wardrobe with sustainability insights',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: Lightbulb,
      title: 'Styling Tips',
      description: 'Learn how to style your items better',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    }
  ];

  const stats = [
    {
      icon: Users,
      label: 'Users Helped',
      value: '10,000+',
      color: 'text-blue-600'
    },
    {
      icon: Shirt,
      label: 'Outfits Created',
      value: '25,000+',
      color: 'text-purple-600'
    },
    {
      icon: Recycle,
      label: 'Sustainable Tips',
      value: '50,000+',
      color: 'text-green-600'
    },
    {
      icon: Star,
      label: 'Satisfaction Rate',
      value: '98%',
      color: 'text-yellow-600'
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Pedi AI Fashion Assistant
            </h1>
            <Sparkles className="h-8 w-8 text-yellow-500" />
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Your personal AI stylist for sustainable fashion. Get personalized advice, 
            outfit suggestions, and wardrobe insights powered by advanced AI.
          </p>
          <div className="flex items-center justify-center gap-2">
            <Badge className="bg-green-100 text-green-800">
              <Recycle className="h-3 w-3 mr-1" />
              Sustainability Focused
            </Badge>
            <Badge className="bg-purple-100 text-purple-800">
              <Sparkles className="h-3 w-3 mr-1" />
              AI Powered
            </Badge>
            <Badge className="bg-blue-100 text-blue-800">
              <Heart className="h-3 w-3 mr-1" />
              Personalized
            </Badge>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4 text-center">
                <div className={`w-10 h-10 ${stat.color.replace('text-', 'bg-').replace('-600', '-100')} rounded-lg flex items-center justify-center mx-auto mb-2`}>
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-600">{stat.label}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => {
              const tabMap: { [key: string]: string } = {
                'AI Chat Assistant': 'chat',
                'Outfit Suggestions': 'outfits',
                'Wardrobe Analysis': 'analysis',
                'Styling Tips': 'tips'
              };
              setActiveTab(tabMap[feature.title] || 'chat');
            }}>
              <CardContent className="p-6 text-center">
                <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                  <feature.icon className={`h-6 w-6 ${feature.color}`} />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main AI Assistant Interface */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-purple-600" />
              AI Fashion Assistant
            </CardTitle>
            <CardDescription>
              Choose a feature below to get started with your AI fashion assistant
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="chat" className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4" />
                  <span className="hidden sm:inline">Chat</span>
                </TabsTrigger>
                <TabsTrigger value="outfits" className="flex items-center gap-2">
                  <Wand2 className="h-4 w-4" />
                  <span className="hidden sm:inline">Outfits</span>
                </TabsTrigger>
                <TabsTrigger value="analysis" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Analysis</span>
                </TabsTrigger>
                <TabsTrigger value="tips" className="flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  <span className="hidden sm:inline">Tips</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="chat" className="mt-6">
                <FashionAssistant className="h-[600px]" />
              </TabsContent>

              <TabsContent value="outfits" className="mt-6">
                <OutfitSuggestions />
              </TabsContent>

              <TabsContent value="analysis" className="mt-6">
                <WardrobeAnalysis />
              </TabsContent>

              <TabsContent value="tips" className="mt-6">
                <StylingTips />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* How It Works */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              How Pedi AI Works
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-purple-600">1</span>
                </div>
                <h3 className="font-semibold mb-2">Analyze Your Style</h3>
                <p className="text-sm text-gray-600">
                  Our AI analyzes your wardrobe, preferences, and style to understand your unique fashion profile.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">2</span>
                </div>
                <h3 className="font-semibold mb-2">Generate Recommendations</h3>
                <p className="text-sm text-gray-600">
                  Get personalized outfit suggestions, styling tips, and sustainable fashion advice tailored to you.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-green-600">3</span>
                </div>
                <h3 className="font-semibold mb-2">Sustainable Fashion</h3>
                <p className="text-sm text-gray-600">
                  Learn how to build a sustainable wardrobe and make eco-friendly fashion choices.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Transform Your Style?
            </h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Start chatting with Pedi AI today and discover new ways to style your wardrobe 
              while making sustainable fashion choices.
            </p>
            <Button 
              onClick={() => setActiveTab('chat')}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              size="lg"
            >
              <MessageCircle className="h-5 w-5 mr-2" />
              Start Chatting with AI
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
