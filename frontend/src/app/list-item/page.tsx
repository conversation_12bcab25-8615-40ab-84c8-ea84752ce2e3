'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import ClothingForm from '@/components/clothing/ClothingForm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle } from 'lucide-react';

interface ClothingFormData {
  title: string;
  description: string;
  category: string;
  subcategory: string;
  brand: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice: string;
  originalPrice: string;
  tags: string;
  preferredSwapCategories: string[];
  location: {
    county: string;
    town: string;
  };
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo: string;
  };
}

export default function ListItemPage() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (data: ClothingFormData, images: File[]) => {
    setLoading(true);
    setError('');

    try {
      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/auth/login');
        return;
      }

      // Create FormData for file upload
      const formData = new FormData();
      
      // Add all form fields
      Object.entries(data).forEach(([key, value]) => {
        if (key === 'sustainabilityInfo') {
          Object.entries(value as ClothingFormData['sustainabilityInfo']).forEach(([subKey, subValue]) => {
            formData.append(`sustainabilityInfo.${subKey}`, String(subValue));
          });
        } else if (key === 'location') {
          Object.entries(value as ClothingFormData['location']).forEach(([subKey, subValue]) => {
            formData.append(`location.${subKey}`, String(subValue));
          });
        } else {
          formData.append(key, String(value));
        }
      });

      // Add images
      images.forEach((image) => {
        formData.append('images', image);
      });

      const response = await fetch('/api/clothing', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to list item');
      }

      setSuccess(true);
      
      // Redirect to item page after 2 seconds
      setTimeout(() => {
        router.push(`/item/${result.data._id}`);
      }, 2000);

    } catch (err) {
      console.error('Error listing item:', err);
      setError(err instanceof Error ? err.message : 'Failed to list item');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="space-y-2">
                <p className="font-medium">Item listed successfully!</p>
                <p className="text-sm">You earned 10 Pedi tokens. Redirecting to your item...</p>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            List Your Clothing Item
          </h1>
          <p className="text-gray-600">
            Share your pre-loved fashion with the Pedi community and contribute to sustainable fashion
          </p>
        </div>

        {error && (
          <div className="mb-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        <ClothingForm onSubmit={handleSubmit} loading={loading} />
      </div>
    </div>
  );
}
