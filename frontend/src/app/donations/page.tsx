'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { 
  Heart, 
  Plus, 
  Package, 
  Building, 
  TrendingUp,
  Users,
  DollarSign,
  Coins,
  Calendar,
  Award
} from 'lucide-react';
import { DonationForm, DonationList } from '@/components/donations';

type DonationView = 'dashboard' | 'create' | 'list';

interface DonationResult {
  id: string;
  status: string;
  message?: string;
}

export default function DonationsPage() {
  const [currentView, setCurrentView] = useState<DonationView>('dashboard');

  // Mock stats - these would come from an API in a real implementation
  const donationStats = {
    totalDonations: 12,
    totalValue: 45600,
    totalItems: 38,
    tokensEarned: 1240,
    charitiesHelped: 6,
    peopleImpacted: 156,
    thisMonthDonations: 3,
    thisMonthValue: 12400
  };

  const recentActivity = [
    {
      id: '1',
      type: 'donation_completed',
      charity: 'Hope Children Center',
      items: 4,
      value: 8500,
      date: '2024-01-15',
      status: 'completed'
    },
    {
      id: '2',
      type: 'donation_accepted',
      charity: 'Women Empowerment Initiative',
      items: 2,
      value: 3200,
      date: '2024-01-12',
      status: 'accepted'
    },
    {
      id: '3',
      type: 'donation_created',
      charity: 'Education for All Foundation',
      items: 6,
      value: 15600,
      date: '2024-01-10',
      status: 'pending'
    }
  ];

  const handleCreateDonation = () => {
    setCurrentView('create');
  };

  const handleDonationSuccess = (donation: DonationResult) => {
    // Handle successful donation creation
    console.log('Donation created successfully:', donation);
    setCurrentView('list'); // Redirect to list view to see the new donation
  };

  const handleCancelDonation = () => {
    setCurrentView('dashboard');
  };

  const formatCurrency = (amount: number) => {
    return `KES ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-KE', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (currentView === 'create') {
    return (
      <div className="container mx-auto px-4 py-8">
        <DonationForm
          onDonationSuccess={handleDonationSuccess}
          onCancel={handleCancelDonation}
        />
      </div>
    );
  }

  if (currentView === 'list') {
    return (
      <div className="container mx-auto px-4 py-8">
        <DonationList
          onCreateNew={handleCreateDonation}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Heart className="h-8 w-8 text-red-500" />
              Donations Dashboard
            </h1>
            <p className="text-gray-600 mt-2">
              Make a difference in your community through clothing donations
            </p>
          </div>
          <Button 
            onClick={handleCreateDonation}
            className="bg-red-600 hover:bg-red-700"
            size="lg"
          >
            <Plus className="h-5 w-5 mr-2" />
            New Donation
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-red-100 rounded-lg">
                  <Heart className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Donations</p>
                  <p className="text-2xl font-bold">{donationStats.totalDonations}</p>
                  <p className="text-xs text-green-600">+{donationStats.thisMonthDonations} this month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(donationStats.totalValue)}</p>
                  <p className="text-xs text-green-600">+{formatCurrency(donationStats.thisMonthValue)} this month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Items Donated</p>
                  <p className="text-2xl font-bold">{donationStats.totalItems}</p>
                  <p className="text-xs text-gray-600">Across all donations</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <Coins className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Tokens Earned</p>
                  <p className="text-2xl font-bold">{donationStats.tokensEarned}</p>
                  <p className="text-xs text-gray-600">From donations</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Impact Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                Your Impact
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Building className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-purple-600">{donationStats.charitiesHelped}</p>
                  <p className="text-sm text-gray-600">Charities Helped</p>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <Users className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-orange-600">{donationStats.peopleImpacted}</p>
                  <p className="text-sm text-gray-600">People Impacted</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-600" />
                Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Heart className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">First Donation</p>
                    <p className="text-xs text-gray-600">Made your first charitable donation</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Package className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Generous Giver</p>
                    <p className="text-xs text-gray-600">Donated 10+ items</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <Building className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Community Helper</p>
                    <p className="text-xs text-gray-600">Helped 5+ different charities</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity & Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setCurrentView('list')}
                >
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                    <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                      <Heart className="h-5 w-5 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-sm">{activity.charity}</p>
                        <Badge className={getStatusColor(activity.status)}>
                          {activity.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600">
                        {activity.items} items • {formatCurrency(activity.value)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={handleCreateDonation}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Donation
              </Button>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => setCurrentView('list')}
              >
                <Package className="h-4 w-4 mr-2" />
                View All Donations
              </Button>
              <Button variant="outline" className="w-full">
                <Building className="h-4 w-4 mr-2" />
                Browse Charities
              </Button>
              <Button variant="outline" className="w-full">
                <TrendingUp className="h-4 w-4 mr-2" />
                View Impact Report
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
