import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5000';

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint') || 'fashion-advice';
    
    // Get the authorization token from the request headers
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, message: 'Authorization token required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Map frontend endpoints to backend endpoints
    const endpointMap: { [key: string]: string } = {
      'fashion-advice': 'fashion-advice',
      'outfit-suggestions': 'outfit-suggestions',
      'wardrobe-analysis': 'wardrobe-analysis',
      'styling-tips': 'styling-tips',
      'generate-description': 'generate-description'
    };

    const backendEndpoint = endpointMap[endpoint];
    if (!backendEndpoint) {
      return NextResponse.json(
        { success: false, message: 'Invalid AI endpoint' },
        { status: 400 }
      );
    }

    const response = await fetch(`${BACKEND_URL}/api/ai/${backendEndpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'AI service request failed' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('AI API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  // For any GET requests that might be needed in the future
  return NextResponse.json(
    { success: false, message: 'GET method not supported for AI endpoints' },
    { status: 405 }
  );
}
