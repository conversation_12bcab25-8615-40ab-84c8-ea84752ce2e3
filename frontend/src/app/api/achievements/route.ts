import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5000';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint') || '';
    const limit = searchParams.get('limit') || '10';
    const category = searchParams.get('category') || '';
    
    // Get authorization header
    const authorization = request.headers.get('authorization');
    
    let url = `${BACKEND_URL}/api/achievements`;
    let headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authorization header if present
    if (authorization) {
      headers.Authorization = authorization;
    }

    // Handle different endpoints
    switch (endpoint) {
      case 'user':
        url = `${BACKEND_URL}/api/achievements/user`;
        break;
      case 'leaderboard':
        url = `${BACKEND_URL}/api/achievements/leaderboard?limit=${limit}`;
        break;
      case 'categories':
        url = `${BACKEND_URL}/api/achievements/categories`;
        break;
      default:
        // Default to all achievements
        if (category) {
          url = `${BACKEND_URL}/api/achievements?category=${category}`;
        }
        break;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'Failed to fetch achievements' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Achievement API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint') || '';
    
    // Get authorization header
    const authorization = request.headers.get('authorization');
    
    if (!authorization) {
      return NextResponse.json(
        { success: false, message: 'Authorization required' },
        { status: 401 }
      );
    }

    let url = `${BACKEND_URL}/api/achievements`;
    let body = {};

    // Handle different endpoints
    switch (endpoint) {
      case 'check':
        url = `${BACKEND_URL}/api/achievements/check`;
        break;
      case 'initialize':
        url = `${BACKEND_URL}/api/achievements/initialize`;
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid endpoint' },
          { status: 400 }
        );
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'Failed to process achievement request' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Achievement API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
