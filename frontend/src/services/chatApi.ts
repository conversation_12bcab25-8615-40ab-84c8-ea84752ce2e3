import { apiClient } from './api';

export interface ChatParticipant {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  isOnline?: boolean;
  lastSeen?: string;
}

export interface ChatMessage {
  id: string;
  sender: ChatParticipant;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: string;
  isRead: boolean;
  deliveryStatus?: 'sending' | 'sent' | 'delivered' | 'failed';
}

export interface ChatConversation {
  id: string;
  participants: ChatParticipant[];
  messages: ChatMessage[];
  lastMessage?: {
    content: string;
    sender: string;
    timestamp: string;
  };
  unreadCount: number;
  relatedClothingItem?: {
    id: string;
    title: string;
    images: string[];
  };
  updatedAt: string;
}

export interface CreateConversationRequest {
  participantId: string;
  clothingItemId?: string;
  initialMessage?: string;
}

export interface SendMessageRequest {
  content: string;
  type?: 'text' | 'image';
}

export interface GetMessagesParams {
  page?: number;
  limit?: number;
  before?: string;
}

export interface MarkMessagesReadRequest {
  messageIds?: string[];
}

class ChatApiService {
  // Get user's conversations
  async getConversations(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<{
    conversations: ChatConversation[];
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalCount: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);

    const endpoint = `/api/chat/conversations${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiClient.get(endpoint, true);
    return response.data;
  }

  // Create a new conversation
  async createConversation(data: CreateConversationRequest): Promise<ChatConversation> {
    const response = await apiClient.post('/api/chat/conversations', data, true);
    return response.data;
  }

  // Get messages for a conversation
  async getMessages(
    conversationId: string,
    params?: GetMessagesParams
  ): Promise<{
    messages: ChatMessage[];
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalCount: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.before) queryParams.append('before', params.before);

    const endpoint = `/api/chat/conversations/${conversationId}/messages${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiClient.get(endpoint, true);
    return response.data;
  }

  // Send a message
  async sendMessage(
    conversationId: string,
    data: SendMessageRequest
  ): Promise<ChatMessage> {
    const response = await apiClient.post(`/api/chat/conversations/${conversationId}/messages`, data, true);
    return response.data;
  }

  // Mark messages as read
  async markMessagesAsRead(
    conversationId: string,
    data?: MarkMessagesReadRequest
  ): Promise<void> {
    await apiClient.patch(`/api/chat/conversations/${conversationId}/messages/read`, data, true);
  }

  // Delete conversation
  async deleteConversation(conversationId: string): Promise<void> {
    await apiClient.delete(`/api/chat/conversations/${conversationId}`, true);
  }

  // Upload image for chat
  async uploadChatImage(file: File): Promise<string> {
    const response = await apiClient.uploadFile('/api/chat/upload-image', file, {}, true);
    return response.data.imageUrl;
  }
}

export const chatApi = new ChatApiService();
