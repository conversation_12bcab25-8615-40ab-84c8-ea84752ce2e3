'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  Lightbulb,
  Shirt,
  Heart,
  Recycle,
  TrendingUp,
  Palette,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  Search,
  Sparkles
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  category: string;
  color: string;
  condition: string;
  brand?: string;
}

interface StylingTipsResponse {
  success: boolean;
  advice?: string;
  recommendations?: {
    tips?: string[];
    sustainabilityScore?: number;
  };
  error?: string;
}

interface StylingTipsProps {
  className?: string;
}

export function StylingTips({ className }: StylingTipsProps) {
  const [selectedItem, setSelectedItem] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [focus, setFocus] = useState<string>('versatility');
  const [searchTerm, setSearchTerm] = useState('');
  const [userItems, setUserItems] = useState<ClothingItem[]>([]);
  const [tips, setTips] = useState<StylingTipsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingItems, setIsLoadingItems] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const focusOptions = [
    { value: 'versatility', label: 'Versatility', icon: Shirt, description: 'How to style items in multiple ways' },
    { value: 'sustainability', label: 'Sustainability', icon: Recycle, description: 'Eco-friendly styling practices' },
    { value: 'trends', label: 'Trends', icon: TrendingUp, description: 'Current fashion trends' },
    { value: 'care', label: 'Care', icon: Heart, description: 'Maintaining and caring for items' },
    { value: 'mixing', label: 'Color Mixing', icon: Palette, description: 'Color coordination and mixing' }
  ];

  const categories = [
    'tops', 'bottoms', 'dresses', 'outerwear', 'shoes', 'accessories', 'activewear', 'formal'
  ];

  // Load user's clothing items
  useEffect(() => {
    loadUserItems();
  }, []);

  const loadUserItems = async () => {
    setIsLoadingItems(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/clothing', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        setUserItems(result.data?.items || []);
      }
    } catch (error) {
      console.error('Error loading user items:', error);
    } finally {
      setIsLoadingItems(false);
    }
  };

  const generateStylingTips = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to get styling tips');
      }

      const requestBody: any = { focus };
      
      if (selectedItem) {
        requestBody.itemId = selectedItem;
      } else if (selectedCategory) {
        requestBody.category = selectedCategory;
      }

      const response = await fetch('/api/ai?endpoint=styling-tips', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to get styling tips');
      }

      setTips(result.data);
    } catch (error: any) {
      console.error('Error getting styling tips:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    generateStylingTips();
  }, [selectedItem, selectedCategory, focus]);

  const handleRefresh = () => {
    generateStylingTips();
  };

  const filteredItems = userItems.filter(item =>
    item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.color.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getSustainabilityBadge = (score?: number) => {
    if (!score) return null;
    
    let color = 'bg-gray-100 text-gray-800';
    let label = 'Low';
    
    if (score >= 70) {
      color = 'bg-green-100 text-green-800';
      label = 'High';
    } else if (score >= 40) {
      color = 'bg-yellow-100 text-yellow-800';
      label = 'Medium';
    }

    return (
      <Badge className={`${color} text-xs`}>
        <Recycle className="h-3 w-3 mr-1" />
        Sustainability: {label}
      </Badge>
    );
  };

  const getSelectedItemDetails = () => {
    if (!selectedItem) return null;
    return userItems.find(item => item._id === selectedItem);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-yellow-600" />
          AI Styling Tips
        </CardTitle>
        <CardDescription>
          Get personalized styling advice for your clothing items
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Controls */}
        <div className="space-y-4">
          {/* Focus Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Focus Area</label>
            <Select value={focus} onValueChange={setFocus}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {focusOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-gray-500">{option.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Item or Category Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Specific Item (Optional)</label>
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search your items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={selectedItem} onValueChange={(value) => {
                  setSelectedItem(value);
                  if (value) setSelectedCategory('');
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an item" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">General tips</SelectItem>
                    {filteredItems.map((item) => (
                      <SelectItem key={item._id} value={item._id}>
                        <div className="flex items-center gap-2">
                          <Shirt className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{item.title}</div>
                            <div className="text-xs text-gray-500">
                              {item.category} • {item.color} • {item.condition}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Or Category</label>
              <Select value={selectedCategory} onValueChange={(value) => {
                setSelectedCategory(value);
                if (value) setSelectedItem('');
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      <div className="capitalize">{category}</div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Selected Item Display */}
          {selectedItem && getSelectedItemDetails() && (
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="flex items-center gap-3">
                <Shirt className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="font-medium text-blue-900">{getSelectedItemDetails()!.title}</h4>
                  <p className="text-sm text-blue-700">
                    {getSelectedItemDetails()!.category} • {getSelectedItemDetails()!.color} • {getSelectedItemDetails()!.condition}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <Button
              onClick={handleRefresh}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh Tips
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-yellow-600" />
              <p className="text-sm text-gray-600">Generating styling tips...</p>
            </div>
          </div>
        )}

        {/* Styling Tips */}
        {tips && !isLoading && (
          <div className="space-y-4">
            {tips.success ? (
              <>
                {/* AI Advice */}
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Sparkles className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-2">AI Styling Advice</h3>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">{tips.advice}</p>
                      
                      {tips.recommendations?.sustainabilityScore && (
                        <div className="mt-3">
                          {getSustainabilityBadge(tips.recommendations.sustainabilityScore)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Additional Tips */}
                {tips.recommendations?.tips && tips.recommendations.tips.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Quick Tips
                    </h3>
                    <div className="space-y-3">
                      {tips.recommendations.tips.map((tip, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          </div>
                          <p className="text-sm text-gray-700">{tip}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Focus Area Info */}
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {React.createElement(focusOptions.find(f => f.value === focus)?.icon || Lightbulb, {
                      className: "h-4 w-4 text-purple-600"
                    })}
                    <h4 className="font-medium text-purple-900">
                      Focus: {focusOptions.find(f => f.value === focus)?.label}
                    </h4>
                  </div>
                  <p className="text-sm text-purple-700">
                    {focusOptions.find(f => f.value === focus)?.description}
                  </p>
                </div>
              </>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {tips.advice || 'Unable to generate styling tips at this time.'}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Loading Items State */}
        {isLoadingItems && (
          <div className="text-center py-4">
            <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600">Loading your wardrobe...</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
