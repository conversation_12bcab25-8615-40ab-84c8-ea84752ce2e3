'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Bot,
  Send,
  User,
  Sparkles,
  Shirt,
  Heart,
  Lightbulb,
  Loader2,
  Recycle,
  Wand2
} from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  sustainabilityScore?: number;
  loading?: boolean;
}

interface FashionAssistantProps {
  className?: string;
}

export function FashionAssistant({ className }: FashionAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: "Hi! I'm <PERSON><PERSON><PERSON>, your AI fashion assistant! 🌟 I'm here to help you with styling advice, outfit suggestions, and sustainable fashion tips. What would you like to know about fashion today?",
      timestamp: new Date(),
      suggestions: [
        "What should I wear for a job interview?",
        "How can I style my denim jacket?",
        "Give me sustainable fashion tips",
        "Analyze my wardrobe"
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    const loadingMessage: Message = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: 'Thinking...',
      timestamp: new Date(),
      loading: true
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to use the AI fashion assistant');
      }

      const response = await fetch('/api/ai?endpoint=fashion-advice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: content,
          includeUserProfile: true,
          context: {
            season: getCurrentSeason(),
            occasion: 'general'
          }
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to get fashion advice');
      }

      const assistantMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: result.data.advice || 'I apologize, but I couldn\'t generate advice for that request.',
        timestamp: new Date(),
        sustainabilityScore: result.data.recommendations?.sustainabilityScore,
        suggestions: result.data.recommendations?.tips?.slice(0, 3)
      };

      setMessages(prev => prev.slice(0, -1).concat(assistantMessage));
    } catch (error: any) {
      console.error('Error getting AI advice:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: error.message || 'Sorry, I\'m having trouble right now. Please try again in a moment!',
        timestamp: new Date()
      };

      setMessages(prev => prev.slice(0, -1).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(inputValue);
  };

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage(suggestion);
  };

  const getCurrentSeason = () => {
    const month = new Date().getMonth();
    // Kenya seasons: Dec-Feb (hot/dry), Mar-May (wet), Jun-Aug (cool/dry), Sep-Nov (hot/dry)
    if (month >= 11 || month <= 1) return 'hot';
    if (month >= 2 && month <= 4) return 'wet';
    if (month >= 5 && month <= 7) return 'cool';
    return 'hot';
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-KE', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getSustainabilityBadge = (score?: number) => {
    if (!score) return null;
    
    let color = 'bg-gray-100 text-gray-800';
    let label = 'Low';
    
    if (score >= 70) {
      color = 'bg-green-100 text-green-800';
      label = 'High';
    } else if (score >= 40) {
      color = 'bg-yellow-100 text-yellow-800';
      label = 'Medium';
    }

    return (
      <Badge className={`${color} text-xs`}>
        <Recycle className="h-3 w-3 mr-1" />
        Sustainability: {label}
      </Badge>
    );
  };

  return (
    <Card className={`h-full flex flex-col ${className}`}>
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <Bot className="h-5 w-5 text-white" />
          </div>
          Pedi AI Fashion Assistant
          <Sparkles className="h-4 w-4 text-yellow-500" />
        </CardTitle>
        <CardDescription>
          Get personalized styling advice and sustainable fashion tips
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.type === 'assistant' && (
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                  {message.loading ? (
                    <Loader2 className="h-4 w-4 text-white animate-spin" />
                  ) : (
                    <Bot className="h-4 w-4 text-white" />
                  )}
                </div>
              )}
              
              <div className={`max-w-[80%] ${message.type === 'user' ? 'order-first' : ''}`}>
                <div
                  className={`rounded-lg p-3 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white ml-auto'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  
                  {message.sustainabilityScore && (
                    <div className="mt-2">
                      {getSustainabilityBadge(message.sustainabilityScore)}
                    </div>
                  )}
                </div>
                
                <div className={`flex items-center gap-2 mt-1 text-xs text-gray-500 ${
                  message.type === 'user' ? 'justify-end' : 'justify-start'
                }`}>
                  <span>{formatTime(message.timestamp)}</span>
                </div>

                {/* Suggestions */}
                {message.suggestions && message.suggestions.length > 0 && (
                  <div className="mt-3 space-y-2">
                    <p className="text-xs text-gray-600 font-medium">Quick suggestions:</p>
                    <div className="flex flex-wrap gap-2">
                      {message.suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="text-xs h-7"
                          onClick={() => handleSuggestionClick(suggestion)}
                          disabled={isLoading}
                        >
                          <Lightbulb className="h-3 w-3 mr-1" />
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {message.type === 'user' && (
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="border-t p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask me about fashion, styling, or sustainable clothing..."
              className="flex-1 min-h-[40px] max-h-[120px] resize-none"
              disabled={isLoading}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
            />
            <Button
              type="submit"
              disabled={!inputValue.trim() || isLoading}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
          
          <div className="flex flex-wrap gap-2 mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSuggestionClick("What should I wear today?")}
              disabled={isLoading}
              className="text-xs"
            >
              <Shirt className="h-3 w-3 mr-1" />
              Daily outfit
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSuggestionClick("Analyze my wardrobe")}
              disabled={isLoading}
              className="text-xs"
            >
              <Wand2 className="h-3 w-3 mr-1" />
              Wardrobe analysis
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSuggestionClick("Give me sustainable fashion tips")}
              disabled={isLoading}
              className="text-xs"
            >
              <Heart className="h-3 w-3 mr-1" />
              Sustainability tips
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
