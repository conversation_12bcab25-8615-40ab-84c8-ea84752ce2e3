'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import {
  Upload,
  X,
  Shirt,
  Coins,
  Recycle,
  AlertCircle
} from 'lucide-react';

interface ClothingFormData {
  title: string;
  description: string;
  category: string;
  subcategory: string;
  brand: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice: string;
  originalPrice: string;
  tags: string;
  preferredSwapCategories: string[];
  location: {
    county: string;
    town: string;
  };
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo: string;
  };
}

const categories = [
  'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
  'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage', 'Underwear'
];

const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'];
const conditions = ['New', 'Like New', 'Good', 'Fair', 'Poor'];
const exchangeTypes = [
  { value: 'swap', label: 'Swap Only', icon: Recycle },
  { value: 'token', label: 'Tokens Only', icon: Coins },
  { value: 'all', label: 'Swap & Tokens', icon: Shirt },
];

interface ClothingFormProps {
  onSubmit: (formData: ClothingFormData, imageFiles: File[]) => Promise<void>;
  loading?: boolean;
}

export default function ClothingForm({ onSubmit, loading = false }: ClothingFormProps) {
  const [formData, setFormData] = useState<ClothingFormData>({
    title: '',
    description: '',
    category: '',
    subcategory: '',
    brand: '',
    size: '',
    color: '',
    condition: '',
    exchangeType: '',
    tokenPrice: '',
    originalPrice: '',
    tags: '',
    preferredSwapCategories: [],
    location: {
      county: '',
      town: '',
    },
    sustainabilityInfo: {
      material: '',
      careInstructions: '',
      estimatedLifespan: '',
      recyclingInfo: '',
    },
  });

  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('sustainabilityInfo.')) {
      const subField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        sustainabilityInfo: {
          ...prev.sustainabilityInfo,
          [subField]: value,
        },
      }));
    } else if (field.startsWith('location.')) {
      const subField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        location: {
          ...prev.location,
          [subField]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSwapCategoryToggle = (category: string) => {
    setFormData(prev => ({
      ...prev,
      preferredSwapCategories: prev.preferredSwapCategories.includes(category)
        ? prev.preferredSwapCategories.filter(c => c !== category)
        : [...prev.preferredSwapCategories, category]
    }));

    // Clear error when user makes selection
    if (errors.preferredSwapCategories) {
      setErrors(prev => ({ ...prev, preferredSwapCategories: '' }));
    }
  };

  const handleImageUpload = (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(file => {
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, images: 'Only image files are allowed' }));
        return false;
      }
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, images: 'Images must be less than 5MB' }));
        return false;
      }
      return true;
    });

    if (images.length + validFiles.length > 5) {
      setErrors(prev => ({ ...prev, images: 'Maximum 5 images allowed' }));
      return;
    }

    setImages(prev => [...prev, ...validFiles]);
    
    // Generate previews
    validFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });

    setErrors(prev => ({ ...prev, images: '' }));
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.size) newErrors.size = 'Size is required';
    if (!formData.color.trim()) newErrors.color = 'Color is required';
    if (!formData.condition) newErrors.condition = 'Condition is required';
    if (!formData.exchangeType) newErrors.exchangeType = 'Exchange type is required';
    if (!formData.location.county.trim()) newErrors['location.county'] = 'County is required';
    if (!formData.location.town.trim()) newErrors['location.town'] = 'Town is required';
    if (!formData.sustainabilityInfo.material.trim()) newErrors['sustainabilityInfo.material'] = 'Material is required';
    if (!formData.sustainabilityInfo.careInstructions.trim()) newErrors['sustainabilityInfo.careInstructions'] = 'Care instructions are required';
    if (!formData.sustainabilityInfo.estimatedLifespan.trim()) newErrors['sustainabilityInfo.estimatedLifespan'] = 'Estimated lifespan is required';

    if (images.length === 0) newErrors.images = 'At least one image is required';

    if (formData.exchangeType === 'token' && !formData.tokenPrice) {
      newErrors.tokenPrice = 'Token price is required for token exchange';
    }

    // Validate preferred swap categories for swap and all exchange types
    if ((formData.exchangeType === 'swap' || formData.exchangeType === 'all') &&
        formData.preferredSwapCategories.length === 0) {
      newErrors.preferredSwapCategories = 'Please select at least one preferred swap category';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData, images);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shirt className="h-6 w-6 text-green-600" />
          List Your Clothing Item
        </CardTitle>
        <CardDescription>
          Share your pre-loved fashion with the Pedi community and earn tokens!
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Image Upload Section */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Photos (Required)</Label>
            
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-gray-300 hover:border-green-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                className="hidden"
                id="image-upload"
              />
              <label htmlFor="image-upload" className="cursor-pointer">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  Drop images here or click to upload
                </p>
                <p className="text-sm text-gray-500">
                  Maximum 5 images, up to 5MB each
                </p>
              </label>
            </div>

            {errors.images && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.images}</AlertDescription>
              </Alert>
            )}

            {/* Image Previews */}
            {imagePreviews.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {imagePreviews.map((preview, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={preview}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., Vintage Denim Jacket"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <Input
                id="brand"
                value={formData.brand}
                onChange={(e) => handleInputChange('brand', e.target.value)}
                placeholder="e.g., Levi's, H&M, Local Brand"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your item's style, fit, and any special features..."
              rows={4}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
          </div>

          {/* Category and Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label>Category *</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
            </div>

            <div className="space-y-2">
              <Label>Size *</Label>
              <Select value={formData.size} onValueChange={(value) => handleInputChange('size', value)}>
                <SelectTrigger className={errors.size ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {sizes.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.size && <p className="text-sm text-red-500">{errors.size}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="color">Color *</Label>
              <Input
                id="color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
                placeholder="e.g., Blue, Red, Multi-color"
                className={errors.color ? 'border-red-500' : ''}
              />
              {errors.color && <p className="text-sm text-red-500">{errors.color}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Condition *</Label>
            <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
              <SelectTrigger className={errors.condition ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select condition" />
              </SelectTrigger>
              <SelectContent>
                {conditions.map((condition) => (
                  <SelectItem key={condition} value={condition}>
                    {condition}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.condition && <p className="text-sm text-red-500">{errors.condition}</p>}
          </div>

          {/* Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="county">County *</Label>
              <Input
                id="county"
                value={formData.location.county}
                onChange={(e) => handleInputChange('location.county', e.target.value)}
                placeholder="e.g., Nairobi, Mombasa, Kisumu"
                className={errors['location.county'] ? 'border-red-500' : ''}
              />
              {errors['location.county'] && <p className="text-sm text-red-500">{errors['location.county']}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="town">Town *</Label>
              <Input
                id="town"
                value={formData.location.town}
                onChange={(e) => handleInputChange('location.town', e.target.value)}
                placeholder="e.g., Westlands, Nyali, Milimani"
                className={errors['location.town'] ? 'border-red-500' : ''}
              />
              {errors['location.town'] && <p className="text-sm text-red-500">{errors['location.town']}</p>}
            </div>
          </div>

          {/* Exchange Type and Pricing */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Exchange Options *</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {exchangeTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => handleInputChange('exchangeType', type.value)}
                    className={`p-4 border rounded-lg text-center transition-colors ${
                      formData.exchangeType === type.value
                        ? 'border-green-500 bg-green-50 text-green-700'
                        : 'border-gray-200 hover:border-green-300'
                    }`}
                  >
                    <Icon className="h-6 w-6 mx-auto mb-2" />
                    <p className="text-sm font-medium">{type.label}</p>
                  </button>
                );
              })}
            </div>
            {errors.exchangeType && <p className="text-sm text-red-500">{errors.exchangeType}</p>}
          </div>

          {/* Pricing */}
          {(formData.exchangeType === 'token' || formData.exchangeType === 'both') && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="tokenPrice">Token Price *</Label>
                <Input
                  id="tokenPrice"
                  type="number"
                  min="1"
                  value={formData.tokenPrice}
                  onChange={(e) => handleInputChange('tokenPrice', e.target.value)}
                  placeholder="e.g., 50"
                  className={errors.tokenPrice ? 'border-red-500' : ''}
                />
                {errors.tokenPrice && <p className="text-sm text-red-500">{errors.tokenPrice}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="originalPrice">Original Price (KES)</Label>
                <Input
                  id="originalPrice"
                  type="number"
                  min="0"
                  value={formData.originalPrice}
                  onChange={(e) => handleInputChange('originalPrice', e.target.value)}
                  placeholder="e.g., 2500"
                />
              </div>
            </div>
          )}

          {/* Preferred Swap Categories */}
          {(formData.exchangeType === 'swap' || formData.exchangeType === 'all') && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-base font-medium">
                  Preferred Swap Categories *
                </Label>
                <p className="text-sm text-gray-600">
                  Select the types of clothes you&apos;d like to receive in exchange for this item
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {categories.map((category) => (
                  <button
                    key={category}
                    type="button"
                    onClick={() => handleSwapCategoryToggle(category)}
                    className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                      formData.preferredSwapCategories.includes(category)
                        ? 'border-brand-pine-green bg-brand-pine-green/10 text-brand-pine-green'
                        : 'border-gray-200 hover:border-brand-pine-green/50 text-gray-700 hover:text-brand-pine-green'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>

              {errors.preferredSwapCategories && (
                <p className="text-sm text-red-500">{errors.preferredSwapCategories}</p>
              )}

              {formData.preferredSwapCategories.length > 0 && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Selected:</span> {formData.preferredSwapCategories.join(', ')}
                </div>
              )}
            </div>
          )}

          {/* Tags */}
          <div className="space-y-2">
            <Label htmlFor="tags">Tags (Optional)</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => handleInputChange('tags', e.target.value)}
              placeholder="e.g., vintage, casual, summer (separate with commas)"
            />
            <p className="text-sm text-gray-500">Add tags to help others find your item</p>
          </div>

          {/* Sustainability Information */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Sustainability Information *</Label>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="material">Material *</Label>
                <Input
                  id="material"
                  value={formData.sustainabilityInfo.material}
                  onChange={(e) => handleInputChange('sustainabilityInfo.material', e.target.value)}
                  placeholder="e.g., 100% Cotton, Polyester blend"
                  className={errors['sustainabilityInfo.material'] ? 'border-red-500' : ''}
                />
                {errors['sustainabilityInfo.material'] && (
                  <p className="text-sm text-red-500">{errors['sustainabilityInfo.material']}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="estimatedLifespan">Estimated Lifespan *</Label>
                <Input
                  id="estimatedLifespan"
                  value={formData.sustainabilityInfo.estimatedLifespan}
                  onChange={(e) => handleInputChange('sustainabilityInfo.estimatedLifespan', e.target.value)}
                  placeholder="e.g., 5+ years, 2-3 years"
                  className={errors['sustainabilityInfo.estimatedLifespan'] ? 'border-red-500' : ''}
                />
                {errors['sustainabilityInfo.estimatedLifespan'] && (
                  <p className="text-sm text-red-500">{errors['sustainabilityInfo.estimatedLifespan']}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="careInstructions">Care Instructions *</Label>
              <Textarea
                id="careInstructions"
                value={formData.sustainabilityInfo.careInstructions}
                onChange={(e) => handleInputChange('sustainabilityInfo.careInstructions', e.target.value)}
                placeholder="e.g., Machine wash cold, hang dry, iron on low heat"
                rows={3}
                className={errors['sustainabilityInfo.careInstructions'] ? 'border-red-500' : ''}
              />
              {errors['sustainabilityInfo.careInstructions'] && (
                <p className="text-sm text-red-500">{errors['sustainabilityInfo.careInstructions']}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="recyclingInfo">Recycling Information (Optional)</Label>
              <Textarea
                id="recyclingInfo"
                value={formData.sustainabilityInfo.recyclingInfo}
                onChange={(e) => handleInputChange('sustainabilityInfo.recyclingInfo', e.target.value)}
                placeholder="e.g., Can be recycled at textile collection points, biodegradable materials"
                rows={2}
              />
            </div>
          </div>

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? 'Listing Item...' : 'List Item & Earn 10 Tokens'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
