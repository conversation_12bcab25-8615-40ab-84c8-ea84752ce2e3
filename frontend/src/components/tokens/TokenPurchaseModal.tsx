'use client';

import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Coins, 
  Smartphone, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Star,
  Zap
} from 'lucide-react';

interface TokenPackage {
  id: string;
  tokens: number;
  priceKES: number;
  priceUSD: number;
  bonus: number;
  description: string;
  popular: boolean;
}

interface TokenPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (tokens: number) => void;
  userBalance: number;
}

const TOKEN_PACKAGES: TokenPackage[] = [
  {
    id: 'basic',
    tokens: 1,
    priceKES: 2,
    priceUSD: 0.02,
    bonus: 0,
    description: 'Basic Package',
    popular: false
  },
  {
    id: 'standard',
    tokens: 2,
    priceKES: 4,
    priceUSD: 0.04,
    bonus: 0,
    description: 'Standard Package',
    popular: false
  },
  {
    id: 'premium',
    tokens: 3,
    priceKES: 6,
    priceUSD: 0.06,
    bonus: 0,
    description: 'Premium Package',
    popular: true
  },
  {
    id: 'deluxe',
    tokens: 4,
    priceKES: 8,
    priceUSD: 0.08,
    bonus: 0,
    description: 'Deluxe Package',
    popular: false
  }
];

export const TokenPurchaseModal: React.FC<TokenPurchaseModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  userBalance
}) => {
  const [selectedPackage, setSelectedPackage] = useState<TokenPackage | null>(null);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');
  const [error, setError] = useState('');
  const [paymentId, setPaymentId] = useState('');

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedPackage(null);
      setPhoneNumber('');
      setLoading(false);
      setPaymentStatus('idle');
      setError('');
      setPaymentId('');
    }
  }, [isOpen]);

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^(254[17]\d{8}|0[17]\d{8})$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('0')) {
      return '254' + cleaned.substring(1);
    }
    return cleaned;
  };

  const handlePurchase = async () => {
    if (!selectedPackage) {
      setError('Please select a token package');
      return;
    }

    if (!phoneNumber || !validatePhoneNumber(phoneNumber)) {
      setError('Please enter a valid Kenyan phone number');
      return;
    }

    setLoading(true);
    setError('');
    setPaymentStatus('processing');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/token-purchase/initiate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          packageId: selectedPackage.id,
          phoneNumber: formatPhoneNumber(phoneNumber),
          paymentMethod: 'mpesa'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPaymentId(data.data.paymentId);
        // Start polling for payment status
        pollPaymentStatus(data.data.paymentId);
      } else {
        throw new Error(data.message || 'Failed to initiate payment');
      }

    } catch (error: any) {
      console.error('Payment initiation failed:', error);
      setError(error.message || 'Failed to initiate payment');
      setPaymentStatus('failed');
    } finally {
      setLoading(false);
    }
  };

  const pollPaymentStatus = async (paymentId: string) => {
    const maxAttempts = 30; // Poll for 5 minutes (30 * 10 seconds)
    let attempts = 0;

    const poll = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/token-purchase/status/${paymentId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const data = await response.json();

        if (data.success) {
          const status = data.data.status;

          if (status === 'completed') {
            setPaymentStatus('success');
            const tokensAwarded = data.data.metadata?.totalTokens || selectedPackage?.tokens || 0;
            setTimeout(() => {
              onSuccess(tokensAwarded);
              onClose();
            }, 2000);
            return;
          } else if (status === 'failed') {
            setPaymentStatus('failed');
            setError('Payment failed. Please try again.');
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000); // Poll every 10 seconds
        } else {
          setPaymentStatus('failed');
          setError('Payment timeout. Please check your M-Pesa messages.');
        }

      } catch (error) {
        console.error('Status polling failed:', error);
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000);
        }
      }
    };

    poll();
  };

  const renderPackageCard = (pkg: TokenPackage) => (
    <Card
      key={pkg.id}
      className={`relative cursor-pointer transition-all duration-200 hover:shadow-lg ${
        selectedPackage?.id === pkg.id
          ? 'ring-2 ring-[#01796F] border-[#01796F] bg-green-50'
          : 'border-gray-200 hover:border-[#01796F]'
      }`}
      onClick={() => setSelectedPackage(pkg)}
    >
      {pkg.popular && (
        <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[#01796F] text-white">
          <Star className="h-3 w-3 mr-1" />
          Popular
        </Badge>
      )}
      
      <CardContent className="p-4 text-center">
        <div className="flex items-center justify-center mb-2">
          <Coins className="h-6 w-6 text-yellow-600 mr-2" />
          <span className="text-2xl font-bold text-[#032221]">{pkg.tokens}</span>
        </div>
        
        <h3 className="font-semibold text-gray-900 mb-1">{pkg.description}</h3>
        
        <div className="text-2xl font-bold text-[#01796F] mb-2">
          KES {pkg.priceKES}
        </div>
        
        {pkg.bonus > 0 && (
          <div className="flex items-center justify-center text-sm text-green-600">
            <Zap className="h-4 w-4 mr-1" />
            +{pkg.bonus} bonus tokens
          </div>
        )}
        
        <div className="text-xs text-gray-500 mt-2">
          {pkg.tokens + pkg.bonus} total tokens
        </div>
      </CardContent>
    </Card>
  );

  const renderPaymentStatus = () => {
    switch (paymentStatus) {
      case 'processing':
        return (
          <Alert className="border-blue-200 bg-blue-50">
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
            <AlertDescription className="text-blue-800">
              Payment initiated! Please check your phone for the M-Pesa prompt and complete the payment.
            </AlertDescription>
          </Alert>
        );
      
      case 'success':
        return (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Payment successful! Your tokens have been added to your account.
            </AlertDescription>
          </Alert>
        );
      
      case 'failed':
        return (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error || 'Payment failed. Please try again.'}
            </AlertDescription>
          </Alert>
        );
      
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Coins className="h-6 w-6 text-yellow-600" />
            Purchase Pedi Tokens
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Balance */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Current Balance:</span>
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-yellow-600" />
                <span className="text-xl font-bold text-yellow-600">{userBalance}</span>
              </div>
            </div>
          </div>

          {/* Package Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Select Token Package</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {TOKEN_PACKAGES.map(renderPackageCard)}
            </div>
          </div>

          {/* Phone Number Input */}
          {selectedPackage && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="phone" className="text-base font-medium">
                  M-Pesa Phone Number
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="254712345678 or 0712345678"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Enter your M-Pesa registered phone number
                </p>
              </div>

              {/* Payment Status */}
              {renderPaymentStatus()}

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                  disabled={loading || paymentStatus === 'processing'}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handlePurchase}
                  disabled={loading || !phoneNumber || paymentStatus === 'processing'}
                  className="flex-1 bg-[#01796F] hover:bg-[#032221]"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Smartphone className="h-4 w-4 mr-2" />
                      Pay KES {selectedPackage.priceKES}
                    </>
                  )}
                </Button>
              </div>

              {/* M-Pesa Info */}
              <Alert>
                <Smartphone className="h-4 w-4" />
                <AlertDescription>
                  You will receive an M-Pesa prompt on your phone. Enter your M-Pesa PIN to complete the payment.
                </AlertDescription>
              </Alert>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
