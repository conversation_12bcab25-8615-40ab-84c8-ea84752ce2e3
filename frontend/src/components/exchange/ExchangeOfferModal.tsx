'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Recycle,
  Coins,
  Check,
  AlertCircle,
  Loader2,
  ShoppingCart
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  images: string[];
  preferredSwapCategories?: string[];
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
  };
}

interface UserItem {
  _id: string;
  title: string;
  category: string;
  size: string;
  condition: string;
  images: string[];
}

interface ExchangeOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetItem: ClothingItem;
  currentUserId: string;
}

export default function ExchangeOfferModal({ 
  isOpen, 
  onClose, 
  targetItem, 
  currentUserId 
}: ExchangeOfferModalProps) {
  const [exchangeType, setExchangeType] = useState<'swap' | 'token_purchase'>('swap');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [userItems, setUserItems] = useState<UserItem[]>([]);
  const [tokenAmount, setTokenAmount] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingUserItems, setLoadingUserItems] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Determine available exchange types based on target item
  const availableTypes = () => {
    const types: Array<{value: 'swap' | 'token_purchase', label: string, icon: any}> = [];
    
    if (targetItem.exchangeType === 'swap' || targetItem.exchangeType === 'all') {
      types.push({ value: 'swap', label: 'Propose Swap', icon: Recycle });
    }
    
    if (targetItem.exchangeType === 'token' || targetItem.exchangeType === 'all') {
      types.push({ value: 'token_purchase', label: 'Buy with Tokens', icon: Coins });
    }
    
    return types;
  };

  // Fetch user's items for swapping
  useEffect(() => {
    if (isOpen && (exchangeType === 'swap')) {
      fetchUserItems();
    }
  }, [isOpen, exchangeType]);

  const fetchUserItems = async () => {
    setLoadingUserItems(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/clothing?owner=' + currentUserId, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        // Filter out the target item and only show available items
        const availableItems = data.data.filter((item: any) => 
          item._id !== targetItem._id && 
          item.isAvailable &&
          (item.exchangeType === 'swap' || item.exchangeType === 'all')
        );
        setUserItems(availableItems);
      }
    } catch (error) {
      console.error('Error fetching user items:', error);
    } finally {
      setLoadingUserItems(false);
    }
  };

  const handleItemToggle = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSubmit = async () => {
    setError('');
    setLoading(true);

    try {
      // Validation
      if (exchangeType === 'swap' && selectedItems.length === 0) {
        throw new Error('Please select at least one item to swap');
      }

      if (exchangeType === 'token_purchase') {
        const amount = parseInt(tokenAmount);
        if (!amount || amount <= 0) {
          throw new Error('Please enter a valid token amount');
        }
        if (targetItem.tokenPrice && amount < targetItem.tokenPrice) {
          throw new Error(`Minimum token amount is ${targetItem.tokenPrice}`);
        }
      }

      const token = localStorage.getItem('token');
      const offerData = {
        type: exchangeType,
        targetUserId: targetItem.owner._id,
        requestedItems: [targetItem._id],
        ...(exchangeType === 'swap' && { offeredItems: selectedItems }),
        ...(exchangeType === 'token_purchase' && { tokenAmount: parseInt(tokenAmount) }),
        ...(message.trim() && { message: message.trim() }),
        preferredDeliveryMethod: 'pickup', // Default for now
      };

      const response = await fetch('/api/exchanges/offers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(offerData),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('API Error Response:', result);
        console.error('Sent data:', offerData);
        throw new Error(result.message || 'Failed to create offer');
      }

      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
        resetForm();
      }, 2000);

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create offer');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedItems([]);
    setTokenAmount('');
    setMessage('');
    setError('');
    setExchangeType('swap');
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      resetForm();
    }
  };

  const types = availableTypes();
  
  // Set default exchange type to first available
  useEffect(() => {
    if (types.length > 0 && !types.find(t => t.value === exchangeType)) {
      setExchangeType(types[0].value);
    }
  }, [targetItem]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5 text-brand-pine-green" />
            Make an Offer for "{targetItem.title}"
          </DialogTitle>
        </DialogHeader>

        {success ? (
          <div className="text-center py-8">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Offer Sent Successfully!</h3>
            <p className="text-gray-600">
              {targetItem.owner.firstName} will be notified about your offer via SMS.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Target Item Display */}
            <Card className="border-brand-pine-green/20">
              <CardContent className="p-4">
                <div className="flex gap-4">
                  <img
                    src={targetItem.images[0] || '/placeholder-image.jpg'}
                    alt={targetItem.title}
                    className="w-20 h-20 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{targetItem.title}</h3>
                    <div className="flex gap-2 mt-1">
                      <Badge variant="outline">{targetItem.category}</Badge>
                      <Badge variant="outline">{targetItem.size}</Badge>
                      <Badge variant="outline">{targetItem.condition}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Owner: {targetItem.owner.firstName} {targetItem.owner.lastName}
                    </p>
                    {targetItem.tokenPrice && (
                      <p className="text-sm font-medium text-brand-pine-green mt-1">
                        Suggested Price: {targetItem.tokenPrice} tokens
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Exchange Type Selection */}
            {types.length > 1 && (
              <div className="space-y-3">
                <Label className="text-base font-medium">Choose Exchange Type</Label>
                <div className="grid grid-cols-2 gap-3">
                  {types.map((type) => {
                    const Icon = type.icon;
                    return (
                      <button
                        key={type.value}
                        type="button"
                        onClick={() => setExchangeType(type.value)}
                        className={`p-4 border rounded-lg text-center transition-colors ${
                          exchangeType === type.value
                            ? 'border-brand-pine-green bg-brand-pine-green/10 text-brand-pine-green'
                            : 'border-gray-200 hover:border-brand-pine-green/50'
                        }`}
                      >
                        <Icon className="h-6 w-6 mx-auto mb-2" />
                        <p className="text-sm font-medium">{type.label}</p>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Content based on exchange type */}
            {exchangeType === 'swap' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">Select Items to Offer</Label>
                  {loadingUserItems && <Loader2 className="h-4 w-4 animate-spin" />}
                </div>
                
                {targetItem.preferredSwapCategories && targetItem.preferredSwapCategories.length > 0 && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Preferred categories:</strong> {targetItem.preferredSwapCategories.join(', ')}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-60 overflow-y-auto">
                  {userItems.length === 0 && !loadingUserItems ? (
                    <div className="col-span-full text-center py-8 text-gray-500">
                      <p>You don't have any items available for swapping.</p>
                      <p className="text-sm mt-1">List some items first to make swap offers!</p>
                    </div>
                  ) : (
                    userItems.map((item) => (
                      <Card
                        key={item._id}
                        className={`cursor-pointer transition-all ${
                          selectedItems.includes(item._id)
                            ? 'border-brand-pine-green bg-brand-pine-green/5'
                            : 'border-gray-200 hover:border-brand-pine-green/50'
                        }`}
                        onClick={() => handleItemToggle(item._id)}
                      >
                        <CardContent className="p-3">
                          <div className="relative">
                            <img
                              src={item.images[0] || '/placeholder-image.jpg'}
                              alt={item.title}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                            {selectedItems.includes(item._id) && (
                              <div className="absolute top-2 right-2 w-6 h-6 bg-brand-pine-green rounded-full flex items-center justify-center">
                                <Check className="h-4 w-4 text-white" />
                              </div>
                            )}
                          </div>
                          <h4 className="font-medium text-sm mt-2 truncate">{item.title}</h4>
                          <div className="flex gap-1 mt-1">
                            <Badge variant="outline" className="text-xs">{item.category}</Badge>
                            <Badge variant="outline" className="text-xs">{item.size}</Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </div>
            )}

            {exchangeType === 'token_purchase' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="tokenAmount">Token Amount *</Label>
                  <Input
                    id="tokenAmount"
                    type="number"
                    min="1"
                    value={tokenAmount}
                    onChange={(e) => setTokenAmount(e.target.value)}
                    placeholder={targetItem.tokenPrice ? `Minimum: ${targetItem.tokenPrice}` : 'Enter amount'}
                  />
                  {targetItem.tokenPrice && (
                    <p className="text-sm text-gray-600">
                      Suggested price: {targetItem.tokenPrice} tokens
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message">Message (Optional)</Label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Add a personal message to your offer..."
                rows={3}
                maxLength={500}
              />
              <p className="text-xs text-gray-500">{message.length}/500 characters</p>
            </div>

            {/* Error Display */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={loading}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending Offer...
                  </>
                ) : (
                  'Send Offer'
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
