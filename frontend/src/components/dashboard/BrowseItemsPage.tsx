'use client';

import { useState, useEffect } from 'react';
import SearchFilters, { SearchFilters as SearchFiltersType } from '@/components/clothing/SearchFilters';
import ClothingGrid from '@/components/clothing/ClothingGrid';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, AlertCircle } from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  originalPrice?: number;
  images: string[];
  views: number;
  likes: string[];
  likeCount: number;
  tags: string[];
  location: {
    county: string;
    town: string;
  };
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    rating: {
      average: number;
      count: number;
    };
    sustainabilityScore: number;
  };
  createdAt: string;
  isAvailable: boolean;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface BrowseItemsPageProps {
  onNavigate?: (page: string) => void;
}

export default function BrowseItemsPage({ onNavigate }: BrowseItemsPageProps) {
  const [items, setItems] = useState<ClothingItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [pagination, setPagination] = useState<Pagination>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    limit: 12,
    hasNext: false,
    hasPrev: false,
  });
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    // Get current user ID from token
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setCurrentUserId(payload.id);
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }

    // Load initial items
    handleSearch({
      search: '',
      category: '',
      size: '',
      condition: '',
      exchangeType: '',
      county: '',
      minPrice: '',
      maxPrice: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }, []);

  const handleSearch = async (filters: SearchFiltersType, page = 1) => {
    setLoading(true);
    setError('');

    try {
      const params = new URLSearchParams();
      
      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value);
        }
      });
      
      params.append('page', page.toString());
      params.append('limit', pagination.limit.toString());

      const response = await fetch(`/api/clothing?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch items');
      }

      setItems(data.data.items);
      setPagination(data.data.pagination);
    } catch (err) {
      console.error('Error fetching items:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch items');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    // Re-run search with new page
    const currentFilters = {
      search: '',
      category: '',
      size: '',
      condition: '',
      exchangeType: '',
      county: '',
      minPrice: '',
      maxPrice: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
    };
    handleSearch(currentFilters, page);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Browse Clothing</h1>
          <p className="text-gray-600 mt-1">
            Discover sustainable fashion from the Pedi community
          </p>
        </div>
        <Button 
          onClick={() => onNavigate?.('list-item')} 
          className="flex items-center gap-2 bg-[#01796F] hover:bg-[#032221]"
        >
          <Plus className="h-4 w-4" />
          List Item
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Search Filters */}
      <SearchFilters onSearch={handleSearch} loading={loading} />

      {/* Results */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            {pagination.totalItems > 0 
              ? `Showing ${((pagination.currentPage - 1) * pagination.limit) + 1}-${Math.min(pagination.currentPage * pagination.limit, pagination.totalItems)} of ${pagination.totalItems} items`
              : 'No items found'
            }
          </p>
        </div>

        <ClothingGrid
          items={items}
          loading={loading}
          currentUserId={currentUserId || undefined}
        />

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPrev}
            >
              Previous
            </Button>
            
            <span className="text-sm text-gray-600">
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>
            
            <Button
              variant="outline"
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNext}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
