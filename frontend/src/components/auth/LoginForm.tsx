'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { CardContent } from '@/components/ui/card';
import { CardDescription } from '@/components/ui/card';
import { CardHeader } from '@/components/ui/card';
import { CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Phone, Shield } from 'lucide-react';

interface LoginFormProps {
  onSwitchToRegister?: () => void;
}

export default function LoginForm({ onSwitchToRegister }: LoginFormProps) {
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  const formatPhoneNumber = (value: string) => {
    const digits = value.replace(/\D/g, '');
    if (digits.startsWith('254')) return '+' + digits;
    if (digits.startsWith('0')) return '+254' + digits.substring(1);
    if (digits.length <= 9) return '+254' + digits;
    return value;
  };

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const formattedPhone = formatPhoneNumber(phoneNumber);

      const response = await fetch('/api/auth/request-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phoneNumber: formattedPhone }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('OTP sent successfully! Please check your SMS.');
        setStep('otp');
        setPhoneNumber(formattedPhone);
      } else {
        setError(data.message || 'Failed to send OTP');
      }
    } catch {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phoneNumber, otp }),
      });

      const data = await response.json();

      if (data.success) {
        localStorage.setItem('token', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        setSuccess('Login successful! Redirecting...');

        setTimeout(() => {
          router.push('/dashboard');
        }, 1000);
      } else {
        setError(data.message || 'Invalid OTP');
      }
    } catch {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/request-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phoneNumber }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('New OTP sent successfully!');
      } else {
        setError(data.message || 'Failed to resend OTP');
      }
    } catch {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="card-modern w-full max-w-md mx-auto shadow-elevated border border-brand-sage-green/30 transition-all duration-300 hover:shadow-glow">
      <CardHeader className="text-center space-y-4 pb-4 pt-8">
        <CardTitle className="flex items-center justify-center gap-2 text-2xl md:text-3xl font-heading text-brand-dark-green">
          <Shield className="h-7 w-7 text-brand-pine-green" />
          Welcome Back to Pedi
        </CardTitle>
        <CardDescription className="text-brand-black font-medium text-sm md:text-base">
          {step === 'phone'
            ? 'Enter your phone number to receive an OTP'
            : 'Enter the OTP sent to your phone'}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6 p-6">
        {error && (
          <Alert variant="destructive" className="animate-fade-in border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}
        {success && (
          <Alert className="bg-brand-light-green border-brand-sage-green animate-fade-in">
            <AlertDescription className="text-brand-dark-green">{success}</AlertDescription>
          </Alert>
        )}

        {step === 'phone' ? (
          <form onSubmit={handlePhoneSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="phone" className="font-medium text-brand-charcoal">
                Phone Number
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-brand-black/70" />
                <Input
                  id="phone"
                  type="tel"
                  placeholder="0712345678 or +254712345678"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="input-modern pl-10 border-brand-sage-green focus:border-brand-pine-green focus:ring-brand-pine-green/20"
                  required
                />
              </div>
              <p className="text-xs text-brand-black">
                We&apos;ll send you an OTP to verify your number
              </p>
            </div>

            <Button
              type="submit"
              className="btn-modern w-full py-3 text-lg font-subheading transition-all-smooth"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending OTP...
                </>
              ) : (
                'Send OTP'
              )}
            </Button>
          </form>
        ) : (
          <form onSubmit={handleOtpSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="otp" className="font-medium text-brand-charcoal">
                Verification Code
              </Label>
              <Input
                id="otp"
                type="text"
                placeholder="Enter 6-digit code"
                value={otp}
                onChange={(e) =>
                  setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))
                }
                maxLength={6}
                className="input-modern text-center text-lg tracking-widest border-brand-sage-green focus:border-brand-pine-green focus:ring-brand-pine-green/20"
                required
              />
              <p className="text-xs text-brand-black">
                Code sent to {phoneNumber}
              </p>
            </div>

            <Button
              type="submit"
              className="btn-modern w-full py-3 text-lg font-subheading transition-all-smooth"
              disabled={loading || otp.length !== 6}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying...
                </>
              ) : (
                'Verify & Login'
              )}
            </Button>

            <div className="flex justify-between text-sm mt-2">
              <button
                type="button"
                onClick={() => setStep('phone')}
                className="text-brand-pine-green hover:text-brand-dark-green hover:underline font-medium transition-colors"
              >
                Change Number
              </button>
              <button
                type="button"
                onClick={handleResendOtp}
                className="text-brand-pine-green hover:text-brand-dark-green hover:underline font-medium transition-colors"
                disabled={loading}
              >
                Resend OTP
              </button>
            </div>
          </form>
        )}

        {onSwitchToRegister && (
          <div className="mt-6 text-center">
            <p className="text-sm text-brand-black">
              Don&apos;t have an account?{' '}
              <button
                onClick={onSwitchToRegister}
                className="text-brand-pine-green hover:text-brand-dark-green hover:underline font-medium transition-colors"
              >
                Sign up here
              </button>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}